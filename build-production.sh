#!/bin/bash

# Script de build optimisé pour la production avec ressources limitées
# Pour MbokaTour

echo "🚀 Début du build de production MbokaTour..."

# Définir les variables d'environnement pour limiter l'utilisation des ressources
export NODE_OPTIONS="--max-old-space-size=2048"
export RAYON_NUM_THREADS=1
export UV_THREADPOOL_SIZE=4

# Nettoyer les caches
echo "🧹 Nettoyage des caches..."
rm -rf node_modules/.vite
rm -rf public/build
rm -rf storage/framework/cache/data/*

# Vérifier l'espace disque disponible
echo "💾 Vérification de l'espace disque..."
df -h .

# Vérifier la mémoire disponible
echo "🧠 Vérification de la mémoire..."
free -h 2>/dev/null || echo "Commande free non disponible (macOS/Windows)"

# Augmenter les limites système si possible
echo "⚙️ Configuration des limites système..."
ulimit -n 4096 2>/dev/null || echo "Impossible de modifier ulimit -n"
ulimit -u 2048 2>/dev/null || echo "Impossible de modifier ulimit -u"

# Build avec gestion d'erreur
echo "🔨 Lancement du build Vite..."
if npm run build; then
    echo "✅ Build réussi !"
    echo "📊 Taille des fichiers générés :"
    du -sh public/build/ 2>/dev/null || echo "Dossier build non trouvé"
    ls -la public/build/ 2>/dev/null || echo "Impossible de lister les fichiers"

    # Vérifier que les fichiers essentiels sont présents
    if [ -f "public/build/manifest.json" ]; then
        echo "✅ Manifest.json présent"
        echo "📄 Contenu du manifest :"
        head -10 public/build/manifest.json
    else
        echo "⚠️ Manifest.json manquant"
    fi

    # Vérifier la présence des assets CSS et JS
    css_files=$(find public/build/assets -name "*.css" 2>/dev/null | wc -l)
    js_files=$(find public/build/assets -name "*.js" 2>/dev/null | wc -l)
    echo "📁 Assets générés : $css_files fichiers CSS, $js_files fichiers JS"

else
    echo "❌ Échec du build"
    echo "🔍 Tentative avec des paramètres encore plus restrictifs..."

    # Tentative avec des paramètres ultra-restrictifs
    export NODE_OPTIONS="--max-old-space-size=1024"
    export RAYON_NUM_THREADS=1

    echo "🔄 Nouvelle tentative de build..."
    if npm run build; then
        echo "✅ Build réussi avec paramètres restrictifs !"
        echo "📊 Taille des fichiers générés :"
        du -sh public/build/ 2>/dev/null || echo "Dossier build non trouvé"
    else
        echo "❌ Échec définitif du build"
        echo "💡 Solutions possibles :"
        echo "   1. Augmenter la mémoire du serveur"
        echo "   2. Utiliser un serveur de build séparé"
        echo "   3. Compiler localement et transférer les fichiers"
        echo "   4. Utiliser une configuration Vite simplifiée"
        echo ""
        echo "🔧 Commandes de diagnostic :"
        echo "   - Vérifier Node.js : node --version"
        echo "   - Vérifier npm : npm --version"
        echo "   - Nettoyer cache : npm cache clean --force"
        echo "   - Réinstaller : rm -rf node_modules && npm install"
        exit 1
    fi
fi

echo "🎉 Build de production MbokaTour terminé !"
