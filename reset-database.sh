#!/bin/bash

# Script pour réinitialiser complètement la base de données MbokaTour
# ATTENTION: Ce script supprime toutes les données existantes !

set -e

echo "🚨 ATTENTION: Ce script va supprimer TOUTES les données de la base de données !"
echo "Êtes-vous sûr de vouloir continuer ? (tapez 'oui' pour confirmer)"
read -r confirmation

if [ "$confirmation" != "oui" ]; then
    echo "❌ Opération annulée."
    exit 1
fi

echo "🔄 Réinitialisation de la base de données MbokaTour..."

# Supprimer toutes les tables
echo "🗑️  Suppression de toutes les tables..."
php artisan db:wipe --force

# Relancer toutes les migrations
echo "📊 Exécution des migrations..."
php artisan migrate --force

# Exécuter les seeders
echo "🌱 Exécution des seeders..."
php artisan db:seed --force

# Créer le lien symbolique pour le stockage
echo "🔗 Création du lien symbolique pour le stockage..."
php artisan storage:link

# Nettoyer les caches
echo "🧹 Nettoyage des caches..."
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo "✅ Base de données réinitialisée avec succès !"
echo ""
echo "📊 Résumé:"
echo "   - Tables créées avec UUID pour les places"
echo "   - Catégories créées"
echo "   - Places d'exemple créées"
echo "   - Permissions et rôles configurés"
echo ""
echo "🔐 Utilisateur de test créé:"
echo "   Email: <EMAIL>"
echo "   Mot de passe: password"
echo ""
echo "🎉 Votre application MbokaTour est prête !"
