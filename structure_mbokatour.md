
# 📦 Structure de la base de données — MbokaTour

## 🧭 1. `places` — Lieux touristiques

| Champ         | Type               | Détail                                 |
|---------------|--------------------|----------------------------------------|
| id            | BIGINT (PK)        | Identifiant unique                     |
| name          | VARCHAR            | Nom du lieu                            |
| description   | TEXT               | Description complète                   |
| location      | VARCHAR            | Quartier / commune                     |
| price         | DECIMAL (nullable) | Prix d'entrée (0 si gratuit)           |
| is_free       | BOOLEAN            | `true` si entrée gratuite              |
| latitude      | DECIMAL (nullable) | Coordonnées GPS                        |
| longitude     | DECIMAL (nullable) | Coordonnées GPS                        |
| timestamps    | TIMESTAMPS         | created_at, updated_at                 |

## 🖼️ 2. `place_images` — Images supplémentaires pour un lieu

| Champ         | Type             | Détail                                 |
|---------------|------------------|----------------------------------------|
| id            | BIGINT (PK)      | Identifiant                            |
| place_id      | BIGINT (FK)      | Référence vers `places`                |
| image_url     | VARCHAR          | Lien ou chemin de l'image              |
| timestamps    | TIMESTAMPS       | created_at, updated_at                 |

## 🎭 3. `events` — Événements culturels ou festifs

| Champ         | Type             | Détail                                 |
|---------------|------------------|----------------------------------------|
| id            | BIGINT (PK)      | Identifiant unique                     |
| title         | VARCHAR          | Titre de l’événement                   |
| description   | TEXT             | Détails                                |
| location      | VARCHAR          | Lieu de l’événement                    |
| start_time    | DATETIME         | Début                                  |
| end_time      | DATETIME         | Fin                                    |
| image         | VARCHAR (nullable) | Image principale                     |
| category_id   | BIGINT (FK)      | Référence vers `categories`            |
| timestamps    | TIMESTAMPS       | created_at, updated_at                 |

## 🗂️ 4. `categories` — Catégories pour lieux & événements

| Champ         | Type             | Détail                                 |
|---------------|------------------|----------------------------------------|
| id            | BIGINT (PK)      | Identifiant unique                     |
| name          | VARCHAR          | Nom de la catégorie                    |
| type          | VARCHAR          | `lieu`, `événement`, ou `mixte`        |
| timestamps    | TIMESTAMPS       | created_at, updated_at                 |

## 🔗 5. `category_place` — Lien lieu ↔ catégories (many-to-many)

| Champ         | Type             | Détail                                 |
|---------------|------------------|----------------------------------------|
| id            | BIGINT (PK)      | Identifiant                            |
| place_id      | BIGINT (FK)      | Référence vers `places`                |
| category_id   | BIGINT (FK)      | Référence vers `categories`            |
| timestamps    | TIMESTAMPS       | created_at, updated_at                 |

## ❤️ 6. `user_place_favorites` — Favoris lieux

| Champ         | Type             | Détail                                 |
|---------------|------------------|----------------------------------------|
| id            | BIGINT (PK)      | Identifiant                            |
| user_id       | BIGINT (FK)      | Référence vers `users`                 |
| place_id      | BIGINT (FK)      | Référence vers `places`                |
| timestamps    | TIMESTAMPS       | created_at, updated_at                 |

## 🌟 7. `user_event_favorites` — Favoris événements

| Champ         | Type             | Détail                                 |
|---------------|------------------|----------------------------------------|
| id            | BIGINT (PK)      | Identifiant                            |
| user_id       | BIGINT (FK)      | Référence vers `users`                 |
| event_id      | BIGINT (FK)      | Référence vers `events`                |
| timestamps    | TIMESTAMPS       | created_at, updated_at                 |
