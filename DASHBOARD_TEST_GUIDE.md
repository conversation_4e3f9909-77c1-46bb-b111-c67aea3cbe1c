# 🧪 Guide de Test du Dashboard MbokaTour

## 🚀 Accès au Dashboard

### 1. <PERSON><PERSON><PERSON><PERSON> le serveur
```bash
php artisan serve
```
Le serveur sera disponible sur `http://127.0.0.1:8001`

### 2. Se connecter
Utilisez l'un de ces comptes de test :

**Administrateur :**
- Email : `<EMAIL>`
- Mot de passe : `password123`

**Utilisateur de test :**
- Email : `<EMAIL>`
- Mot de passe : `password`

### 3. Accéder au Dashboard
Une fois connecté, allez sur : `http://127.0.0.1:8001/dashboard`

## 📊 Données de Test Disponibles

### Statistiques Générales
- ✅ **24 lieux** créés avec images et vidéos
- ✅ **3+ utilisateurs** avec différents rôles
- ✅ **50+ likes** répartis sur les lieux
- ✅ **6+ commentaires** avec notes
- ✅ **10+ favoris** par utilisateurs
- ✅ **Catégories** variées (restaurants, parcs, musées, etc.)

### Données Réalistes
- **Vues** : Entre 50 et 500 vues par lieu
- **Likes** : Distribution aléatoire sur les lieux
- **Commentaires** : Avec notes de 3 à 5 étoiles
- **Favoris** : 2-4 favoris par utilisateur

## 🎯 Fonctionnalités à Tester

### Cartes de Statistiques
- [ ] Vérifier l'affichage des totaux (lieux, utilisateurs, likes, commentaires)
- [ ] Contrôler les tendances hebdomadaires (pourcentages)
- [ ] Tester les couleurs et icônes des cartes
- [ ] Vérifier la responsivité sur mobile/tablette

### Graphique d'Activité
- [ ] Changer de métrique (lieux, utilisateurs, likes, commentaires)
- [ ] Survoler les barres pour voir les tooltips
- [ ] Vérifier les totaux et moyennes affichés
- [ ] Tester sur différentes tailles d'écran

### Lieux Populaires
- [ ] Vérifier le classement par vues
- [ ] Contrôler l'affichage des images
- [ ] Tester les badges de catégories
- [ ] Vérifier les métriques (vues, likes, commentaires)

### Lieux les Plus Likés
- [ ] Vérifier le classement par likes
- [ ] Contrôler l'affichage sans images
- [ ] Tester les catégories affichées

### Commentaires Récents
- [ ] Vérifier l'affichage des avatars
- [ ] Contrôler les notes en étoiles
- [ ] Tester les dates relatives
- [ ] Vérifier la troncature du contenu

### Utilisateurs Actifs
- [ ] Vérifier le classement par activité
- [ ] Contrôler les métriques (likes, favoris, commentaires)
- [ ] Tester les avatars avec initiales
- [ ] Vérifier les badges d'activité

### Statistiques par Catégories
- [ ] Vérifier les barres de progression
- [ ] Contrôler les couleurs des catégories
- [ ] Tester les icônes emoji
- [ ] Vérifier les compteurs

## 🐛 Points de Test Spécifiques

### Performance
- [ ] Temps de chargement < 2 secondes
- [ ] Pas d'erreurs dans la console
- [ ] Images qui se chargent correctement
- [ ] Animations fluides

### Responsive Design
- [ ] **Mobile** (< 768px) : Cartes empilées
- [ ] **Tablette** (768px-1024px) : Grille 2 colonnes
- [ ] **Desktop** (> 1024px) : Grille complète

### Erreurs Potentielles
- [ ] Images manquantes → Placeholder affiché
- [ ] Données vides → Messages "Aucun élément"
- [ ] Erreurs de calcul → Vérifier les pourcentages
- [ ] Problèmes de style → Classes Tailwind v4

## 🔧 Dépannage

### Erreurs Communes

**1. Page blanche ou erreur 500**
```bash
php artisan config:clear
php artisan cache:clear
php artisan view:clear
```

**2. Données manquantes**
```bash
php artisan db:seed
```

**3. Erreurs Tailwind CSS**
```bash
npm run build
# ou
npm run dev
```

**4. Problèmes d'authentification**
- Vérifier que l'utilisateur existe
- Réinitialiser le mot de passe si nécessaire

### Logs Utiles
```bash
# Logs Laravel
tail -f storage/logs/laravel.log

# Logs du serveur
php artisan serve --verbose
```

## ✅ Checklist de Validation

### Fonctionnel
- [ ] Toutes les statistiques s'affichent
- [ ] Les graphiques sont interactifs
- [ ] Les images se chargent
- [ ] Les liens fonctionnent
- [ ] Les animations sont fluides

### Visuel
- [ ] Couleurs MbokaTour respectées (#fcc804)
- [ ] Typographie cohérente
- [ ] Espacement harmonieux
- [ ] Icônes appropriées
- [ ] Design responsive

### Performance
- [ ] Chargement rapide
- [ ] Pas d'erreurs console
- [ ] Mémoire optimisée
- [ ] Requêtes efficaces

---

## 🎉 Dashboard Prêt !

Une fois tous les tests validés, votre dashboard MbokaTour est prêt pour la production ! 

**Prochaines étapes :**
1. Configurer la mise en cache des statistiques
2. Ajouter des filtres par période
3. Implémenter les notifications temps réel
4. Optimiser pour de gros volumes de données

*Bon test ! 🚀*
