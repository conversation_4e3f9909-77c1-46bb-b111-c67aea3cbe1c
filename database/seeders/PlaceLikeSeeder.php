<?php

namespace Database\Seeders;

use App\Models\Place;
use App\Models\PlaceLike;
use App\Models\User;
use Illuminate\Database\Seeder;

class PlaceLikeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users and places
        $users = User::all();
        $places = Place::where('is_active', true)->get();

        if ($users->isEmpty() || $places->isEmpty()) {
            $this->command->info('No users or places found. Please run UserSeeder and PlaceSeeder first.');
            return;
        }

        $this->command->info('Creating place likes...');

        // Create random likes
        $likesCreated = 0;
        
        foreach ($places as $place) {
            // Each place gets liked by 0-5 random users
            $likeCount = rand(0, min(5, $users->count()));
            $randomUsers = $users->random($likeCount);
            
            foreach ($randomUsers as $user) {
                // Check if like already exists to avoid duplicates
                if (!PlaceLike::where('user_id', $user->id)->where('place_id', $place->id)->exists()) {
                    PlaceLike::create([
                        'user_id' => $user->id,
                        'place_id' => $place->id,
                        'created_at' => now()->subDays(rand(0, 30)), // Random date within last 30 days
                    ]);
                    $likesCreated++;
                }
            }
        }

        $this->command->info("Created {$likesCreated} place likes.");

        // Show some statistics
        $totalLikes = PlaceLike::count();
        $placesWithLikes = Place::whereHas('likes')->count();
        $usersWhoLiked = User::whereHas('placeLikes')->count();

        $this->command->info("Statistics:");
        $this->command->info("- Total likes: {$totalLikes}");
        $this->command->info("- Places with likes: {$placesWithLikes}");
        $this->command->info("- Users who liked places: {$usersWhoLiked}");
    }
}
