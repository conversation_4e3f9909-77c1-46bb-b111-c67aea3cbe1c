<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $placeCategories = [
            ['name' => 'Nature & Évasion', 'type' => 'place', 'description' => 'Parcs, forêts, jardins botaniques'],
            ['name' => 'Monuments & Histoire', 'type' => 'place', 'description' => 'Sites historiques, statues, musées'],
            ['name' => 'Plages & Bords de Fleuve', 'type' => 'place', 'description' => 'Plages du fleuve, espaces détente nautiques'],
            ['name' => 'Art & Culture', 'type' => 'place', 'description' => 'Galeries, ateliers, centres culturels'],
            ['name' => 'Vie Nocturne', 'type' => 'place', 'description' => 'Bars, clubs, lounges'],
            ['name' => 'Restaurants & Gastronomie', 'type' => 'place', 'description' => 'Spots culinaires locaux et internationaux'],
            ['name' => 'Marchés & Artisanat', 'type' => 'place', 'description' => 'Marchés, boutiques de produits locaux'],
            ['name' => 'Activités en Famille', 'type' => 'place', 'description' => 'Espaces pour enfants, parcs à thème'],
            ['name' => 'Spiritualité & Lieux sacrés', 'type' => 'place', 'description' => 'Églises, temples, lieux de méditation'],
            ['name' => 'Points de Vue & Panorama', 'type' => 'place', 'description' => 'Collines, terrasses avec vue, rooftops'],
        ];

        $eventCategories = [
            ['name' => 'Concerts & Festivals', 'type' => 'event', 'description' => 'Musique, danse, arts vivants'],
            ['name' => 'Conférences & Talks', 'type' => 'event', 'description' => 'Tech, business, éducation, société'],
            ['name' => 'Ateliers & Formations', 'type' => 'event', 'description' => 'DIY, artisanat, cuisine, développement perso'],
            ['name' => 'Rencontres sociales', 'type' => 'event', 'description' => 'Afterworks, soirées privées, networking'],
            ['name' => 'Sport & Bien-être', 'type' => 'event', 'description' => 'Yoga, randonnées, compétitions sportives'],
            ['name' => 'Cinéma & Projections', 'type' => 'event', 'description' => 'Films en plein air ou en salle'],
            ['name' => 'Foires & Expositions', 'type' => 'event', 'description' => 'Expo d\'art, salons, marchés temporaires'],
            ['name' => 'Événements religieux', 'type' => 'event', 'description' => 'Messes, cultes, célébrations spécifiques'],
            ['name' => 'Mode & Beauté', 'type' => 'event', 'description' => 'Défilés, salons beauté'],
            ['name' => 'Jeux & Compétitions', 'type' => 'event', 'description' => 'E-sport, quizz, tournois locaux'],
        ];

        foreach ($placeCategories as $category) {
            Category::create($category);
        }

        foreach ($eventCategories as $category) {
            Category::create($category);
        }
    }
}
