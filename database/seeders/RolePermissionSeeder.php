<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Créer les permissions
        $permissions = [
            'manage-places',
            'manage-events',
            'manage-categories',
            'manage-users',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Créer le rôle admin
        $adminRole = Role::firstOrCreate(['name' => 'admin']);

        // Assigner toutes les permissions au rôle admin
        $adminRole->givePermissionTo($permissions);

        // Créer un utilisateur admin par défaut
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin MbokaTour',
                'phone_number' => '+243123456789',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ]
        );

        // Assigner le rôle admin à l'utilisateur
        $adminUser->assignRole('admin');

        $this->command->info('Rôles et permissions créés avec succès!');
        $this->command->info('Utilisateur admin créé: <EMAIL> / password123');
    }
}
