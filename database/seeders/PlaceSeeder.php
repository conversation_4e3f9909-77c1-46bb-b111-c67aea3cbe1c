<?php

namespace Database\Seeders;

use App\Models\Place;
use App\Models\Category;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PlaceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Récupérer les catégories de type 'place'
        $categories = Category::where('type', 'place')->get();

        if ($categories->isEmpty()) {
            $this->command->warn('Aucune catégorie de type "place" trouvée. Veuillez d\'abord exécuter CategorySeeder.');
            return;
        }

        $places = [
            [
                'name' => 'Parc de la Gombe',
                'description' => 'Un magnifique parc urbain au cœur de Kinshasa, parfait pour les promenades en famille et la détente.',
                'location' => 'Gombe, Kinshasa',
                'address' => 'Avenue de la Paix, Gombe',
                'neighborhood' => 'Gombe',
                'city' => 'Kinshasa',
                'latitude' => -4.3247,
                'longitude' => 15.3147,
                'price' => null,
                'is_free' => true,
                'opening_hours' => '06:00 - 18:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 1,
                'category_names' => ['Nature & Évasion', 'Activités en Famille']
            ],
            [
                'name' => 'Marché Central de Kinshasa',
                'description' => 'Le plus grand marché de la capitale, où vous trouverez tout ce dont vous avez besoin, des produits locaux aux objets d\'artisanat.',
                'location' => 'Centre-ville, Kinshasa',
                'address' => 'Boulevard du 30 Juin',
                'neighborhood' => 'Centre-ville',
                'city' => 'Kinshasa',
                'latitude' => -4.3297,
                'longitude' => 15.3089,
                'price' => null,
                'is_free' => true,
                'opening_hours' => '05:00 - 19:00',
                'status' => 'active',
                'main_image_url' => 'https://images.unsplash.com/photo-1555529669-e69e7aa0ba9a?w=800&h=600&fit=crop',
                'mediaType' => 'image',
                'is_active' => true,
                'is_featured' => false,
                'views_count' => rand(50, 500),
                'priority' => 2,
                'category_names' => ['Marchés & Artisanat']
            ]
        ];

        // Ajouter plus de places avec des vidéos et images variées
        $additionalPlaces = $this->getAdditionalPlaces();
        $videoPlaces = $this->getMorePlacesWithVideos();
        $unsplashPlaces = $this->getUnsplashPlaces();
        $allPlaces = array_merge($places, $additionalPlaces, $videoPlaces, $unsplashPlaces);

        $this->createPlacesWithMoreData($allPlaces, $categories);
    }

    private function getAdditionalPlaces(): array
    {
        return [
            [
                'name' => 'Plage de Kinkole',
                'description' => 'Une belle plage sur les rives du fleuve Congo, idéale pour se détendre et profiter du coucher de soleil.',
                'location' => 'Kinkole, Kinshasa',
                'address' => 'Route de Kinkole',
                'neighborhood' => 'Kinkole',
                'city' => 'Kinshasa',
                'latitude' => -4.4089,
                'longitude' => 15.5647,
                'price' => 5.00,
                'is_free' => false,
                'opening_hours' => '08:00 - 20:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 1,
                'category_names' => ['Plages & Bords de Fleuve', 'Activités en Famille']
            ],
            [
                'name' => 'Musée National de Kinshasa',
                'description' => 'Découvrez l\'histoire et la culture du Congo à travers des expositions fascinantes.',
                'location' => 'Gombe, Kinshasa',
                'address' => 'Avenue des Cliniques',
                'neighborhood' => 'Gombe',
                'city' => 'Kinshasa',
                'latitude' => -4.3189,
                'longitude' => 15.3078,
                'price' => 3.00,
                'is_free' => false,
                'opening_hours' => '09:00 - 17:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerEscapes.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => false,
                'views_count' => rand(50, 500),
                'priority' => 3,
                'category_names' => ['Monuments & Histoire', 'Art & Culture']
            ],
            [
                'name' => 'Restaurant Chez Maman Colonelle',
                'description' => 'Savourez la cuisine congolaise authentique dans ce restaurant familial réputé.',
                'location' => 'Matonge, Kinshasa',
                'address' => 'Avenue Tombalbaye',
                'neighborhood' => 'Matonge',
                'city' => 'Kinshasa',
                'latitude' => -4.3456,
                'longitude' => 15.3234,
                'price' => 15.00,
                'is_free' => false,
                'opening_hours' => '11:00 - 23:00',
                'status' => 'active',
                'main_image_url' => 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&h=600&fit=crop',
                'mediaType' => 'image',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 2,
                'category_names' => ['Restaurants & Gastronomie']
            ]
        ];
    }

    private function createPlacesWithMoreData(array $places, $categories): void
    {
        foreach ($places as $placeData) {
            // Extraire les noms des catégories
            $categoryNames = $placeData['category_names'];
            unset($placeData['category_names']);

            // Créer la place
            $place = Place::create($placeData);

            // Associer les catégories
            $categoriesToAttach = $categories->whereIn('name', $categoryNames)->pluck('id');
            if ($categoriesToAttach->isNotEmpty()) {
                $place->categories()->attach($categoriesToAttach);
            }

            $this->command->info("Place créée: {$place->name} (Type: {$place->mediaType})");
        }

        $this->command->info('Toutes les places ont été créées avec succès !');
    }

    private function getMorePlacesWithVideos(): array
    {
        return [
            [
                'name' => 'Cathédrale Notre-Dame du Congo',
                'description' => 'Magnifique cathédrale au cœur de Kinshasa, lieu de spiritualité et d\'architecture remarquable.',
                'location' => 'Gombe, Kinshasa',
                'address' => 'Avenue de la Cathédrale',
                'neighborhood' => 'Gombe',
                'city' => 'Kinshasa',
                'latitude' => -4.3267,
                'longitude' => 15.3123,
                'price' => null,
                'is_free' => true,
                'opening_hours' => '06:00 - 19:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 1,
                'category_names' => ['Spiritualité & Lieux sacrés', 'Monuments & Histoire']
            ],
            [
                'name' => 'Jardin Zoologique de Kinshasa',
                'description' => 'Découvrez la faune locale et exotique dans ce zoo familial au cœur de la capitale.',
                'location' => 'Gombe, Kinshasa',
                'address' => 'Avenue de la Zoo',
                'neighborhood' => 'Gombe',
                'city' => 'Kinshasa',
                'latitude' => -4.3134,
                'longitude' => 15.2967,
                'price' => 2.00,
                'is_free' => false,
                'opening_hours' => '08:00 - 17:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerJoyrides.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => false,
                'views_count' => rand(50, 500),
                'priority' => 2,
                'category_names' => ['Nature & Évasion', 'Activités en Famille']
            ]
        ];
    }

    private function getUnsplashPlaces(): array
    {
        return [
            [
                'name' => 'Académie des Beaux-Arts',
                'description' => 'Centre artistique et culturel proposant des expositions d\'art contemporain africain.',
                'location' => 'Gombe, Kinshasa',
                'address' => 'Avenue des Arts',
                'neighborhood' => 'Gombe',
                'city' => 'Kinshasa',
                'latitude' => -4.3178,
                'longitude' => 15.3089,
                'price' => 1.50,
                'is_free' => false,
                'opening_hours' => '09:00 - 18:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerMeltdowns.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => false,
                'views_count' => rand(50, 500),
                'priority' => 3,
                'category_names' => ['Art & Culture']
            ],
            [
                'name' => 'Marché de la Liberté',
                'description' => 'Marché nocturne animé avec de la musique live et des spécialités locales.',
                'location' => 'Kalamu, Kinshasa',
                'address' => 'Avenue de la Liberté',
                'neighborhood' => 'Kalamu',
                'city' => 'Kinshasa',
                'latitude' => -4.3423,
                'longitude' => 15.3278,
                'price' => null,
                'is_free' => true,
                'opening_hours' => '18:00 - 02:00',
                'status' => 'active',
                'main_image_url' => 'https://images.unsplash.com/photo-1533900298318-6b8da08a523e?w=800&h=600&fit=crop',
                'mediaType' => 'image',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 1,
                'category_names' => ['Vie Nocturne', 'Marchés & Artisanat']
            ],
            [
                'name' => 'Terrasse Panoramique Ngaliema',
                'description' => 'Point de vue exceptionnel sur le fleuve Congo et la ville de Brazzaville.',
                'location' => 'Ngaliema, Kinshasa',
                'address' => 'Mont Ngaliema',
                'neighborhood' => 'Ngaliema',
                'city' => 'Kinshasa',
                'latitude' => -4.2834,
                'longitude' => 15.2645,
                'price' => 2.50,
                'is_free' => false,
                'opening_hours' => '06:00 - 20:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/Sintel.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 1,
                'category_names' => ['Points de Vue & Panorama', 'Nature & Évasion']
            ],
            [
                'name' => 'Centre Commercial Kintambo',
                'description' => 'Centre commercial moderne avec restaurants, boutiques et espaces de loisirs.',
                'location' => 'Kintambo, Kinshasa',
                'address' => 'Boulevard Kintambo',
                'neighborhood' => 'Kintambo',
                'city' => 'Kinshasa',
                'latitude' => -4.3389,
                'longitude' => 15.2978,
                'price' => null,
                'is_free' => true,
                'opening_hours' => '09:00 - 21:00',
                'status' => 'active',
                'main_image_url' => 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop',
                'mediaType' => 'image',
                'is_active' => true,
                'is_featured' => false,
                'views_count' => rand(50, 500),
                'priority' => 2,
                'category_names' => ['Marchés & Artisanat', 'Activités en Famille']
            ],
            [
                'name' => 'Stade des Martyrs',
                'description' => 'Le plus grand stade du Congo, lieu emblématique pour les événements sportifs et culturels.',
                'location' => 'Kalamu, Kinshasa',
                'address' => 'Avenue des Martyrs',
                'neighborhood' => 'Kalamu',
                'city' => 'Kinshasa',
                'latitude' => -4.3467,
                'longitude' => 15.3289,
                'price' => 5.00,
                'is_free' => false,
                'opening_hours' => '08:00 - 22:00',
                'status' => 'active',
                'main_image_url' => 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/TearsOfSteel.mp4',
                'mediaType' => 'video',
                'is_active' => true,
                'is_featured' => true,
                'views_count' => rand(50, 500),
                'priority' => 1,
                'category_names' => ['Monuments & Histoire', 'Activités en Famille']
            ]
        ];
    }
}
