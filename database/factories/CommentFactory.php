<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Comment>
 */
class CommentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'place_id' => \App\Models\Place::factory(),
            'user_id' => \App\Models\User::factory(),
            'content' => $this->faker->paragraph(2),
            'rating' => $this->faker->optional(0.7)->numberBetween(1, 5), // 70% chance d'avoir une note
            'is_approved' => true,
        ];
    }
}
