<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Place>
 */
class PlaceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'location' => $this->faker->address(),
            'price' => $this->faker->randomFloat(2, 0, 100),
            'is_free' => $this->faker->boolean(30),
            'latitude' => $this->faker->latitude(-6, -2), // Around DRC
            'longitude' => $this->faker->longitude(12, 30), // Around DRC
            'address' => $this->faker->streetAddress(),
            'neighborhood' => $this->faker->citySuffix(),
            'city' => $this->faker->city(),
            'opening_hours' => '09:00 - 18:00',
            'status' => 'active',
            'main_image_url' => $this->faker->imageUrl(800, 600, 'places'),
            'mediaType' => $this->faker->randomElement(['image', 'video']),
            'is_active' => true,
            'is_featured' => $this->faker->boolean(20),
            'views_count' => $this->faker->numberBetween(0, 1000),
            'priority' => $this->faker->numberBetween(0, 10),
        ];
    }
}
