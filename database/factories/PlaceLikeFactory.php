<?php

namespace Database\Factories;

use App\Models\Place;
use App\Models\PlaceLike;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PlaceLike>
 */
class PlaceLikeFactory extends Factory
{
    protected $model = PlaceLike::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'place_id' => Place::factory(),
        ];
    }
}
