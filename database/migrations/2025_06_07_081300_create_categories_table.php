<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('categories', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // 'place' ou 'event'
            $table->text('description')->nullable();
            $table->string('icon')->nullable(); // Pour stocker le nom de l'icône (ex: 'restaurant', 'museum', etc.)
            $table->string('color')->default('#fcc804'); // Couleur de la catégorie (par défaut jaune MbokaTour)
            $table->integer('display_order')->default(0); // Pour ordonner les catégories
            $table->timestamps();

            // Index pour optimiser les requêtes
            $table->index(['type', 'display_order']);
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('categories');
    }
};
