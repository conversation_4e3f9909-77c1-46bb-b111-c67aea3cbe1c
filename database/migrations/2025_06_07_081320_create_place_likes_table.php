<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('place_likes', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->uuid('place_id'); // UUID car nous avons migré les places vers UUID
            $table->timestamps();

            // Clé étrangère vers places avec UUID
            $table->foreign('place_id')->references('id')->on('places')->onDelete('cascade');

            // Empêcher qu'un utilisateur like plusieurs fois le même lieu
            $table->unique(['user_id', 'place_id']);
            
            // Index pour optimiser les requêtes
            $table->index(['place_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('place_likes');
    }
};
