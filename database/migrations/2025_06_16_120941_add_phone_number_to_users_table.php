<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Ajouter le champ phone_number comme nullable d'abord
            $table->string('phone_number')->nullable()->unique()->after('name');

            // Rendre le champ email nullable
            $table->string('email')->nullable()->change();

            // Supprimer l'index unique sur email s'il existe
            $table->dropUnique(['email']);
        });

        // Mettre à jour les utilisateurs existants avec un numéro de téléphone par défaut
        // En production, vous devriez demander aux utilisateurs de mettre à jour leurs profils
        DB::table('users')->whereNull('phone_number')->update([
            'phone_number' => DB::raw('CONCAT("+33", SUBSTR(REPLACE(REPLACE(name, " ", ""), "-", ""), 1, 9))')
        ]);

        // Maintenant rendre phone_number requis
        Schema::table('users', function (Blueprint $table) {
            $table->string('phone_number')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Supprimer le champ phone_number
            $table->dropColumn('phone_number');

            // Remettre email comme requis et unique
            $table->string('email')->nullable(false)->change();
            $table->unique('email');
        });
    }
};
