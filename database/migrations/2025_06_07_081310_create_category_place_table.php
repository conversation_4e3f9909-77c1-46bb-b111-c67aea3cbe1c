<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('category_place', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->uuid('place_id'); // UUID car nous avons migré les places vers UUID
            $table->timestamps();

            // Clé étrangère vers places avec UUID
            $table->foreign('place_id')->references('id')->on('places')->onDelete('cascade');

            // Index pour optimiser les requêtes
            $table->index(['category_id', 'place_id']);
            $table->index(['place_id', 'category_id']);

            // Empêcher les doublons
            $table->unique(['category_id', 'place_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('category_place');
    }
};
