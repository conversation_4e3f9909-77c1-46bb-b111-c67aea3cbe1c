<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->id();
            $table->uuid('place_id'); // UUID car nous avons migré les places vers UUID
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->text('content');
            $table->integer('rating')->nullable(); // Note de 1 à 5 étoiles (optionnel)
            $table->boolean('is_approved')->default(true); // Pour modération future
            $table->timestamps();

            // Clé étrangère vers places avec UUID
            $table->foreign('place_id')->references('id')->on('places')->onDelete('cascade');

            // Index pour optimiser les requêtes
            $table->index(['place_id', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['is_approved', 'created_at']);
            $table->index(['rating', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('comments');
    }
};
