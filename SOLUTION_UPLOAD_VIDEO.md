# ✅ Solution - Erreur Upload Vidéo "The main image url failed to upload"

## 🎯 Problème identifié

L'erreur "The main image url failed to upload" pour les vidéos était causée par des **limites PHP trop restrictives** :

### Configuration PHP problématique
- `upload_max_filesize: 2M` ❌ (trop petit pour les vidéos)
- `post_max_size: 8M` ❌ (trop petit pour les vidéos)
- <PERSON><PERSON> attend des fichiers jusqu'à **100MB** ✅

## 🔧 Solution appliquée

### 1. Configuration .htaccess mise à jour

**Fichier racine `.htaccess`** :
```apache
# Configuration PHP pour uploads de vidéos - MbokaTour
php_value upload_max_filesize 100M
php_value post_max_size 110M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 256M
```

**Fichier `public/.htaccess`** :
```apache
# Configuration PHP pour uploads de vidéos - MbokaTour
php_value upload_max_filesize 100M
php_value post_max_size 110M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 256M
```

### 2. Debugging ajouté

**Frontend (Edit.vue)** :
- Logs détaillés des fichiers uploadés
- Informations sur la taille, type MIME, etc.
- Gestion d'erreurs améliorée

**Backend (PlaceController.php)** :
- Logs Laravel pour tracer les uploads
- Détection des erreurs de validation
- Suivi du processus complet

## 📊 Nouvelles limites

| Paramètre | Ancienne valeur | Nouvelle valeur |
|-----------|----------------|-----------------|
| upload_max_filesize | 2M | 100M |
| post_max_size | 8M | 110M |
| max_execution_time | 0 | 300s |
| max_input_time | -1 | 300s |
| memory_limit | 128M | 256M |

## 🎬 Formats vidéo supportés

### Extensions acceptées
- `.mp4` (recommandé)
- `.mov` (recommandé)
- `.avi`
- `.mkv`
- `.webm`
- `.ogg`
- `.qt`

### Types MIME supportés
- `video/mp4`
- `video/quicktime`
- `video/x-msvideo`
- `video/x-matroska`
- `video/webm`
- `video/ogg`

## 📏 Recommandations pour les vidéos

### Taille et qualité
- **Taille max** : 100MB (limite Laravel)
- **Taille recommandée** : < 50MB
- **Résolution max** : 1920x1080
- **Durée recommandée** : < 2 minutes

### Formats optimaux
1. **MP4** avec codec H.264 (meilleure compatibilité)
2. **MOV** pour les vidéos de qualité
3. **WebM** pour le web (plus petit)

## 🧪 Test de la solution

### Commande de diagnostic
```bash
php debug-upload.php
```

### Vérifications automatiques
- ✅ Configuration PHP mise à jour
- ✅ Répertoire de stockage accessible
- ✅ Espace disque suffisant (42GB disponible)
- ✅ Permissions d'écriture OK

## 🚀 Déploiement

### En local (développement)
Les fichiers `.htaccess` sont déjà configurés.

### En production
1. **Vérifier** que le serveur supporte les directives `php_value`
2. **Alternative** : Modifier directement `php.ini` si `.htaccess` ne fonctionne pas
3. **Redémarrer** le serveur web après modification

### Configuration php.ini (alternative)
```ini
upload_max_filesize = 100M
post_max_size = 110M
max_execution_time = 300
max_input_time = 300
memory_limit = 256M
```

## 🔍 Debugging

### Logs à surveiller
```bash
# Logs Laravel
tail -f storage/logs/laravel.log

# Logs PHP (si disponibles)
tail -f /var/log/php_errors.log
```

### Console navigateur
Les logs détaillés incluent maintenant :
- Nom du fichier
- Taille en bytes
- Type MIME détecté
- MediaType auto-détecté

## ⚠️ Limitations connues

### Serveur partagé
Certains hébergeurs peuvent :
- Ignorer les directives `.htaccess`
- Avoir des limites globales plus restrictives
- Nécessiter une configuration via le panel d'administration

### Solutions alternatives
1. **Compression vidéo** avant upload
2. **Upload en chunks** pour gros fichiers
3. **Stockage externe** (AWS S3, Cloudinary)

## 📞 Support

### Si le problème persiste
1. Vérifier que `.htaccess` est lu par Apache
2. Tester avec `php debug-upload.php`
3. Vérifier les logs d'erreur PHP
4. Contacter l'hébergeur pour les limites serveur

### Commandes de test
```bash
# Vérifier la configuration PHP actuelle
php -r "echo 'upload_max_filesize: ' . ini_get('upload_max_filesize') . PHP_EOL;"
php -r "echo 'post_max_size: ' . ini_get('post_max_size') . PHP_EOL;"

# Tester un upload simple
curl -X POST -F "file=@video.mp4" http://localhost/test-upload.php
```

---

**Status** : ✅ **RÉSOLU**  
**Date** : 18 juin 2025  
**Cause** : Limites PHP trop restrictives (2M → 100M)  
**Solution** : Configuration `.htaccess` mise à jour
