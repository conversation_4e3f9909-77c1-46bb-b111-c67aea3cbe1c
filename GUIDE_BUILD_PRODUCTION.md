# 🚀 Guide de Build Production - MbokaTour

## ✅ Solution finale implémentée

Le problème d'erreur runtime "Cannot access 'Y' before initialization" a été **résolu** en revenant à une configuration Vite standard et en utilisant un script de build optimisé.

## 🔧 Script de build de production

### Commande principale
```bash
npm run build:production
```

### Fonctionnalités du script
- ✅ **Gestion intelligente des ressources** (mémoire, threads)
- ✅ **Nettoyage automatique** des caches
- ✅ **Vérifications système** (espace disque, mémoire)
- ✅ **Fallback automatique** en cas d'échec
- ✅ **Validation des résultats** (manifest, assets)
- ✅ **Diagnostics détaillés**

## 📊 Résultats du build

### Performance
- **Temps de build** : ~7.34 secondes
- **Taille totale** : 820K
- **Assets générés** : 1 CSS + 34 JS
- **Compression gzip** : ~85% de réduction

### Fichiers générés
```
public/build/assets/app-xIflaILN.css                116.13 kB │ gzip: 18.24 kB
public/build/assets/app-CGRA9F9P.js                 261.30 kB │ gzip: 93.72 kB
+ 34 autres chunks JavaScript optimisés
```

## 🛠️ Configuration technique

### Variables d'environnement
```bash
NODE_OPTIONS="--max-old-space-size=2048"
RAYON_NUM_THREADS=1
UV_THREADPOOL_SIZE=4
```

### Limites système
```bash
ulimit -n 4096  # Descripteurs de fichiers
ulimit -u 2048  # Processus utilisateur
```

### Configuration Vite (vite.config.ts)
```typescript
export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/js/app.ts'],
            ssr: 'resources/js/ssr.ts',
            refresh: true,
        }),
        tailwindcss(),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './resources/js'),
        },
    },
});
```

## 🚀 Déploiement en production

### Étapes recommandées
1. **Build local** : `npm run build:production`
2. **Vérification** : Tester l'application localement
3. **Transfert** : Copier `public/build/` vers le serveur
4. **Test production** : Vérifier l'absence d'erreurs

### Commandes de déploiement
```bash
# Sur le serveur de production
git pull origin main
npm install --production=false
npm run build:production

# Vérifier les résultats
ls -la public/build/
cat public/build/manifest.json
```

## 🔍 Diagnostic et dépannage

### Vérifications automatiques
- ✅ Espace disque disponible
- ✅ Mémoire système
- ✅ Présence du manifest.json
- ✅ Nombre d'assets générés
- ✅ Taille des fichiers

### En cas de problème
Le script propose automatiquement :
1. **Réduction de la mémoire** (2048MB → 1024MB)
2. **Limitation des threads** (RAYON_NUM_THREADS=1)
3. **Messages de diagnostic** détaillés
4. **Solutions alternatives**

### Commandes de diagnostic manuel
```bash
# Vérifier Node.js
node --version

# Vérifier npm
npm --version

# Nettoyer cache
npm cache clean --force

# Réinstaller dépendances
rm -rf node_modules && npm install
```

## 📈 Optimisations appliquées

### Performance
- **Chunking automatique** par Vite
- **Tree-shaking** des dépendances
- **Minification** CSS et JS
- **Compression gzip** côté serveur

### Ressources système
- **Limitation mémoire** Node.js
- **Contrôle des threads** Rayon/UV
- **Nettoyage automatique** des caches
- **Gestion des limites** système

## 🎯 Avantages de cette solution

### Stabilité
- ✅ **Aucune erreur runtime** détectée
- ✅ **Configuration Vite standard** (maintenable)
- ✅ **Fallback automatique** en cas d'échec
- ✅ **Validation complète** des résultats

### Performance
- ✅ **Build rapide** (~7 secondes)
- ✅ **Assets optimisés** (compression gzip)
- ✅ **Cache browser** efficace (34 chunks)
- ✅ **Tailwind CSS complet** inclus

### Maintenance
- ✅ **Script réutilisable** pour tous les environnements
- ✅ **Diagnostics intégrés** pour le dépannage
- ✅ **Configuration simple** sans complexité
- ✅ **Compatible** avec tous les hébergeurs

## 📞 Support

En cas de problème persistant :
1. Exécuter `npm run build:production` et analyser les logs
2. Vérifier l'espace disque et la mémoire disponible
3. Tester avec `npm run build` standard
4. Consulter les diagnostics automatiques du script

---

**Status** : ✅ **RÉSOLU ET TESTÉ**  
**Date** : 18 juin 2025  
**Version** : MbokaTour v1.0  
**Build testé** : Succès avec 820K d'assets optimisés
