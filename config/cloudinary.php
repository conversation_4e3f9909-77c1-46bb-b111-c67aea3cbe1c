<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Cloudinary Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Cloudinary settings. All of the configuration
    | options are documented, and you can override them by setting environment
    | variables.
    |
    */

    'cloud_name' => env('CLOUDINARY_CLOUD_NAME'),
    'api_key' => env('CLOUDINARY_API_KEY'),
    'api_secret' => env('CLOUDINARY_API_SECRET'),
    'secure' => env('CLOUDINARY_SECURE', true),

    /*
    |--------------------------------------------------------------------------
    | Upload Settings
    |--------------------------------------------------------------------------
    |
    | Configure default upload settings for your application.
    |
    */

    'upload_preset' => env('CLOUDINARY_UPLOAD_PRESET'),
    'notification_url' => env('CLOUDINARY_NOTIFICATION_URL'),

    /*
    |--------------------------------------------------------------------------
    | Archive Settings
    |--------------------------------------------------------------------------
    |
    | Configure archive settings for your application.
    |
    */

    'archive' => [
        'type' => env('CLOUDINARY_ARCHIVE_TYPE', 'upload'),
        'target_format' => env('CLOUDINARY_ARCHIVE_TARGET_FORMAT'),
    ],

    /*
    |--------------------------------------------------------------------------
    | File Settings
    |--------------------------------------------------------------------------
    |
    | Configure file settings for your application.
    |
    */

    'file' => [
        'overwrite' => env('CLOUDINARY_FILE_OVERWRITE', true),
        'unique_filename' => env('CLOUDINARY_FILE_UNIQUE_FILENAME', true),
        'use_filename' => env('CLOUDINARY_FILE_USE_FILENAME', false),
    ],
];
