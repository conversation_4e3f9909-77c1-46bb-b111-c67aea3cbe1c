# Guide de Migration vers Cloudinary - MbokaTour

## Vue d'ensemble

Ce guide détaille la migration du stockage local vers Cloudinary pour la gestion des images et vidéos dans MbokaTour.

## Changements apportés

### 1. Installation et Configuration

#### Packages installés
```bash
composer require cloudinary-labs/cloudinary-laravel
```

#### Fichiers de configuration ajoutés/modifiés
- `config/cloudinary.php` - Configuration Cloudinary
- `config/filesystems.php` - Ajout du driver Cloudinary
- `.env` - Variables d'environnement Cloudinary

#### Variables d'environnement requises
```env
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
CLOUDINARY_SECURE=true
```

### 2. Nouveaux Services

#### CloudinaryService (`app/Services/CloudinaryService.php`)
Service dédié pour gérer les interactions avec Cloudinary :
- Upload de fichiers (images et vidéos)
- Suppression de fichiers
- Génération d'URLs optimisées
- Extraction de public_id depuis les URLs
- Détection automatique du type de ressource

### 3. Modifications du PlaceService

#### Changements principaux
- Remplacement du stockage local par Cloudinary
- Utilisation du CloudinaryService pour tous les uploads
- Gestion automatique de la détection du type de média
- Suppression des fichiers depuis Cloudinary lors des suppressions

#### Méthodes modifiées
- `createPlace()` - Upload vers Cloudinary
- `updatePlace()` - Gestion des mises à jour avec Cloudinary
- `deletePlace()` - Suppression depuis Cloudinary
- `uploadPlaceImages()` - Upload multiple vers Cloudinary

## Configuration Cloudinary

### 1. Créer un compte Cloudinary
1. Allez sur [cloudinary.com](https://cloudinary.com)
2. Créez un compte gratuit
3. Récupérez vos clés depuis le dashboard

### 2. Configuration des variables d'environnement
Ajoutez ces variables dans votre fichier `.env` :

```env
CLOUDINARY_CLOUD_NAME=votre_cloud_name
CLOUDINARY_API_KEY=votre_api_key
CLOUDINARY_API_SECRET=votre_api_secret
CLOUDINARY_SECURE=true
```

### 3. Structure des dossiers Cloudinary
Les fichiers sont organisés comme suit :
- Images principales : `mbokatour/places/main/`
- Images multiples : `mbokatour/places/images/`

## Migration des données existantes

### Option 1 : Migration automatique (recommandée pour peu de données)
```php
// Script de migration à exécuter une fois
php artisan make:command MigrateToCloudinary
```

### Option 2 : Migration manuelle
1. Téléchargez les images existantes depuis `storage/app/public/`
2. Uploadez-les manuellement sur Cloudinary
3. Mettez à jour les URLs en base de données

## Tests et Validation

### 1. Script de test
Exécutez le script de test fourni :
```bash
php test_cloudinary_integration.php
```

### 2. Tests manuels
1. **Upload d'image** : Créez une place avec une image
2. **Upload de vidéo** : Créez une place avec une vidéo
3. **Suppression** : Supprimez une place et vérifiez la suppression sur Cloudinary
4. **Mise à jour** : Modifiez une image et vérifiez le remplacement

### 3. Vérification des logs
```bash
tail -f storage/logs/laravel.log
```

## Avantages de Cloudinary

### Performance
- CDN global pour une livraison rapide
- Optimisation automatique des images
- Formats adaptatifs (WebP, AVIF)
- Compression intelligente

### Fonctionnalités
- Transformations d'images à la volée
- Support vidéo avancé
- Détection automatique du contenu
- APIs robustes

### Scalabilité
- Stockage illimité (selon le plan)
- Bande passante élevée
- Gestion automatique des pics de trafic

## Dépannage

### Erreurs communes

#### 1. "Invalid API credentials"
- Vérifiez vos clés dans le fichier `.env`
- Assurez-vous qu'il n'y a pas d'espaces dans les valeurs

#### 2. "Upload failed"
- Vérifiez la taille du fichier (limite selon votre plan)
- Vérifiez le format du fichier
- Consultez les logs Laravel

#### 3. "File not found on Cloudinary"
- Vérifiez que le public_id est correct
- Assurez-vous que le fichier existe sur Cloudinary

### Commandes utiles

```bash
# Vérifier la configuration
php artisan config:show cloudinary

# Nettoyer le cache de configuration
php artisan config:clear

# Voir les logs en temps réel
tail -f storage/logs/laravel.log

# Tester la connexion Cloudinary
php test_cloudinary_integration.php
```

## Rollback (si nécessaire)

Pour revenir au stockage local :

1. Modifiez `FILESYSTEM_DISK=public` dans `.env`
2. Restaurez l'ancien `PlaceService.php`
3. Supprimez les références à `CloudinaryService`

## Support

- Documentation Cloudinary : [cloudinary.com/documentation](https://cloudinary.com/documentation)
- Package Laravel : [github.com/cloudinary-labs/cloudinary-laravel](https://github.com/cloudinary-labs/cloudinary-laravel)
- Logs de l'application : `storage/logs/laravel.log`
