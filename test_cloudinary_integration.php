<?php

/**
 * Script de test pour l'intégration Cloudinary
 * 
 * Ce script teste les fonctionnalités de base de Cloudinary :
 * - Upload d'images
 * - Upload de vidéos
 * - Suppression de fichiers
 * - Génération d'URLs optimisées
 */

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\CloudinaryService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

// Simuler l'environnement Laravel pour les tests
if (!function_exists('env')) {
    function env($key, $default = null) {
        return $_ENV[$key] ?? $default;
    }
}

// Charger les variables d'environnement
if (file_exists(__DIR__ . '/.env')) {
    $lines = file(__DIR__ . '/.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $_ENV[trim($key)] = trim($value);
        }
    }
}

echo "=== Test d'intégration Cloudinary pour MbokaTour ===\n\n";

// Vérifier la configuration
echo "1. Vérification de la configuration Cloudinary...\n";
$cloudName = env('CLOUDINARY_CLOUD_NAME');
$apiKey = env('CLOUDINARY_API_KEY');
$apiSecret = env('CLOUDINARY_API_SECRET');

if (empty($cloudName) || empty($apiKey) || empty($apiSecret)) {
    echo "❌ Configuration Cloudinary manquante!\n";
    echo "Veuillez configurer les variables suivantes dans votre fichier .env :\n";
    echo "- CLOUDINARY_CLOUD_NAME\n";
    echo "- CLOUDINARY_API_KEY\n";
    echo "- CLOUDINARY_API_SECRET\n\n";
    echo "Vous pouvez obtenir ces informations depuis votre dashboard Cloudinary :\n";
    echo "https://cloudinary.com/console\n";
    exit(1);
}

echo "✅ Configuration Cloudinary trouvée\n";
echo "   Cloud Name: {$cloudName}\n";
echo "   API Key: " . substr($apiKey, 0, 8) . "...\n\n";

// Test de connexion basique
echo "2. Test de connexion à Cloudinary...\n";
try {
    // Initialiser Cloudinary
    \Cloudinary\Configuration\Configuration::instance([
        'cloud' => [
            'cloud_name' => $cloudName,
            'api_key' => $apiKey,
            'api_secret' => $apiSecret,
        ],
        'url' => [
            'secure' => true
        ]
    ]);

    // Test simple avec l'API Admin
    $api = new \Cloudinary\Api\Admin\AdminApi();
    $result = $api->ping();
    
    if (isset($result['status']) && $result['status'] === 'ok') {
        echo "✅ Connexion à Cloudinary réussie!\n\n";
    } else {
        echo "❌ Problème de connexion à Cloudinary\n";
        print_r($result);
        exit(1);
    }
} catch (Exception $e) {
    echo "❌ Erreur de connexion : " . $e->getMessage() . "\n";
    exit(1);
}

// Test des URLs d'optimisation
echo "3. Test de génération d'URLs optimisées...\n";
try {
    $testPublicId = 'sample';
    $baseUrl = "https://res.cloudinary.com/{$cloudName}/image/upload";
    $optimizedUrl = "{$baseUrl}/q_auto,f_auto,w_300,h_200,c_fill/{$testPublicId}";

    echo "✅ URL optimisée générée :\n";
    echo "   {$optimizedUrl}\n\n";
} catch (Exception $e) {
    echo "❌ Erreur lors de la génération d'URL : " . $e->getMessage() . "\n\n";
}

// Instructions pour les tests manuels
echo "4. Tests manuels recommandés :\n\n";

echo "a) Test d'upload d'image :\n";
echo "   - Créez une nouvelle place avec une image\n";
echo "   - Vérifiez que l'image est uploadée sur Cloudinary\n";
echo "   - Vérifiez que l'URL stockée en base commence par 'https://res.cloudinary.com'\n\n";

echo "b) Test d'upload de vidéo :\n";
echo "   - Créez une nouvelle place avec une vidéo\n";
echo "   - Vérifiez que la vidéo est uploadée sur Cloudinary\n";
echo "   - Vérifiez que le mediaType est automatiquement détecté comme 'video'\n\n";

echo "c) Test de suppression :\n";
echo "   - Supprimez une place avec des médias\n";
echo "   - Vérifiez que les fichiers sont supprimés de Cloudinary\n";
echo "   - Consultez les logs pour confirmer les suppressions\n\n";

echo "d) Test de mise à jour :\n";
echo "   - Modifiez l'image principale d'une place\n";
echo "   - Vérifiez que l'ancienne image est supprimée de Cloudinary\n";
echo "   - Vérifiez que la nouvelle image est uploadée\n\n";

echo "5. Commandes utiles :\n\n";
echo "   # Voir les logs Laravel\n";
echo "   tail -f storage/logs/laravel.log\n\n";
echo "   # Tester l'API des places\n";
echo "   curl -X GET http://mbokatour.test/api/places\n\n";
echo "   # Vérifier la configuration\n";
echo "   php artisan config:show cloudinary\n\n";

echo "=== Fin des tests ===\n";
echo "Si tous les tests passent, votre intégration Cloudinary est prête!\n";
echo "N'oubliez pas de configurer vos vraies clés Cloudinary dans le fichier .env\n";
