<?php
// Test de configuration upload pour MbokaTour
header('Content-Type: text/plain; charset=utf-8');

echo "=== TEST CONFIGURATION UPLOAD WEB ===\n\n";

echo "1. CONFIGURATION PHP WEB:\n";
echo "upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "post_max_size: " . ini_get('post_max_size') . "\n";
echo "max_execution_time: " . ini_get('max_execution_time') . "\n";
echo "memory_limit: " . ini_get('memory_limit') . "\n";
echo "max_input_time: " . ini_get('max_input_time') . "\n";
echo "file_uploads: " . (ini_get('file_uploads') ? 'Enabled' : 'Disabled') . "\n";
echo "\n";

echo "2. SERVEUR WEB:\n";
echo "Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "\n";
echo "PHP Version: " . PHP_VERSION . "\n";
echo "SAPI: " . PHP_SAPI . "\n";
echo "\n";

echo "3. HTACCESS STATUS:\n";
$htaccessPath = __DIR__ . '/.htaccess';
echo "Fichier .htaccess: " . (file_exists($htaccessPath) ? 'Existe' : 'Manquant') . "\n";
if (file_exists($htaccessPath)) {
    $content = file_get_contents($htaccessPath);
    echo "Contient php_value: " . (strpos($content, 'php_value') !== false ? 'Oui' : 'Non') . "\n";
}
echo "\n";

echo "4. TEST UPLOAD SIMPLE:\n";
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    $file = $_FILES['test_file'];
    echo "Fichier reçu: " . $file['name'] . "\n";
    echo "Taille: " . $file['size'] . " bytes\n";
    echo "Type: " . $file['type'] . "\n";
    echo "Erreur: " . $file['error'] . "\n";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        echo "✅ Upload réussi!\n";
    } else {
        $errors = [
            UPLOAD_ERR_INI_SIZE => 'Fichier trop volumineux (php.ini)',
            UPLOAD_ERR_FORM_SIZE => 'Fichier trop volumineux (formulaire)',
            UPLOAD_ERR_PARTIAL => 'Upload partiel',
            UPLOAD_ERR_NO_FILE => 'Aucun fichier',
            UPLOAD_ERR_NO_TMP_DIR => 'Répertoire temporaire manquant',
            UPLOAD_ERR_CANT_WRITE => 'Erreur d\'écriture',
            UPLOAD_ERR_EXTENSION => 'Extension bloquée'
        ];
        echo "❌ Erreur: " . ($errors[$file['error']] ?? 'Inconnue') . "\n";
    }
} else {
    echo "Aucun fichier uploadé. Utilisez le formulaire ci-dessous.\n";
}

echo "\n=== FORMULAIRE DE TEST ===\n";
?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Upload MbokaTour</title>
    <style>
        body { font-family: monospace; margin: 20px; }
        form { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        input[type="file"] { margin: 10px 0; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; border-radius: 3px; }
    </style>
</head>
<body>
    <h2>Test Upload Configuration</h2>
    <form method="POST" enctype="multipart/form-data">
        <p>Sélectionnez un fichier vidéo pour tester l'upload :</p>
        <input type="file" name="test_file" accept="video/*" required>
        <br>
        <button type="submit">Tester Upload</button>
    </form>
    
    <h3>Limites actuelles :</h3>
    <ul>
        <li>upload_max_filesize: <?= ini_get('upload_max_filesize') ?></li>
        <li>post_max_size: <?= ini_get('post_max_size') ?></li>
        <li>memory_limit: <?= ini_get('memory_limit') ?></li>
    </ul>
</body>
</html>
