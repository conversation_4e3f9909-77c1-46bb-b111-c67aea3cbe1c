// Test simple pour vérifier les améliorations de gestion d'images
// Ce fichier peut être utilisé dans la console du navigateur pour tester les fonctions

console.log('🧪 Tests de gestion d\'images - Edit.vue');

// Test 1: Validation des fichiers
function testFileValidation() {
    console.log('\n📋 Test 1: Validation des fichiers');
    
    // Simuler différents types de fichiers
    const testFiles = [
        { name: 'image.jpg', size: 1024 * 1024, type: 'image/jpeg' }, // Valide
        { name: 'video.mp4', size: 50 * 1024 * 1024, type: 'video/mp4' }, // Valide
        { name: 'document.pdf', size: 1024 * 1024, type: 'application/pdf' }, // Invalide
        { name: 'huge-file.jpg', size: 200 * 1024 * 1024, type: 'image/jpeg' }, // Trop gros
    ];
    
    const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
    const allowedVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/webm', 'video/ogg'];
    const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];
    const maxSize = 100 * 1024 * 1024; // 100MB
    
    function validateFile(file) {
        if (file.size > maxSize) {
            return { isValid: false, error: `Le fichier "${file.name}" est trop volumineux (max 100MB)` };
        }
        
        if (!allowedTypes.includes(file.type)) {
            return { isValid: false, error: `Le type de fichier "${file.type}" n'est pas autorisé` };
        }
        
        return { isValid: true };
    }
    
    testFiles.forEach(file => {
        const result = validateFile(file);
        console.log(`${result.isValid ? '✅' : '❌'} ${file.name}: ${result.isValid ? 'Valide' : result.error}`);
    });
}

// Test 2: Gestion des URLs de prévisualisation
function testPreviewUrlManagement() {
    console.log('\n🖼️ Test 2: Gestion des URLs de prévisualisation');
    
    const previewUrls = new Map();
    
    function createPreviewUrl(file) {
        const fileKey = `${file.name}-${file.size}-${file.lastModified}`;
        
        if (previewUrls.has(fileKey)) {
            console.log(`♻️ Réutilisation de l'URL pour ${file.name}`);
            return previewUrls.get(fileKey);
        }
        
        // Simuler URL.createObjectURL
        const url = `blob:${Math.random().toString(36).substr(2, 9)}`;
        previewUrls.set(fileKey, url);
        console.log(`🆕 Nouvelle URL créée pour ${file.name}: ${url}`);
        return url;
    }
    
    function cleanupPreviewUrls() {
        console.log(`🧹 Nettoyage de ${previewUrls.size} URLs`);
        previewUrls.forEach((url, key) => {
            console.log(`   Révocation: ${key} -> ${url}`);
        });
        previewUrls.clear();
    }
    
    // Test avec des fichiers simulés
    const file1 = { name: 'test1.jpg', size: 1024, lastModified: Date.now() };
    const file2 = { name: 'test2.jpg', size: 2048, lastModified: Date.now() };
    
    createPreviewUrl(file1);
    createPreviewUrl(file2);
    createPreviewUrl(file1); // Devrait réutiliser l'URL existante
    
    cleanupPreviewUrls();
}

// Test 3: Détection du type de média
function testMediaTypeDetection() {
    console.log('\n🎬 Test 3: Détection du type de média');
    
    function getMediaType(file) {
        const extension = file.name.split('.').pop()?.toLowerCase();
        
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp'];
        const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'ogg', 'qt'];
        
        if (extension && imageExtensions.includes(extension)) {
            return 'image';
        } else if (extension && videoExtensions.includes(extension)) {
            return 'video';
        } else {
            return 'image'; // Default
        }
    }
    
    const testFiles = [
        { name: 'photo.jpg' },
        { name: 'video.mp4' },
        { name: 'animation.gif' },
        { name: 'movie.mov' },
        { name: 'unknown.xyz' }
    ];
    
    testFiles.forEach(file => {
        const mediaType = getMediaType(file);
        console.log(`📁 ${file.name} -> ${mediaType}`);
    });
}

// Test 4: Simulation de suppression d'image
function testImageDeletion() {
    console.log('\n🗑️ Test 4: Simulation de suppression d\'image');
    
    let localImages = [
        { id: 1, image_url: 'image1.jpg' },
        { id: 2, image_url: 'image2.jpg' },
        { id: 3, image_url: 'image3.jpg' }
    ];
    
    function deleteImage(imageId) {
        console.log(`🎯 Tentative de suppression de l'image ${imageId}`);
        
        const imageIndex = localImages.findIndex(img => img.id === imageId);
        if (imageIndex > -1) {
            const deletedImage = localImages[imageIndex];
            localImages = [...localImages];
            localImages.splice(imageIndex, 1);
            
            console.log(`✅ Image ${imageId} (${deletedImage.image_url}) supprimée`);
            console.log(`📊 Images restantes: ${localImages.length}`);
            console.log('   Liste:', localImages.map(img => `${img.id}:${img.image_url}`));
        } else {
            console.log(`❌ Image ${imageId} non trouvée`);
        }
    }
    
    deleteImage(2);
    deleteImage(1);
    deleteImage(5); // N'existe pas
    deleteImage(3);
}

// Exécuter tous les tests
function runAllTests() {
    console.log('🚀 Démarrage des tests de gestion d\'images\n');
    
    testFileValidation();
    testPreviewUrlManagement();
    testMediaTypeDetection();
    testImageDeletion();
    
    console.log('\n✨ Tous les tests terminés !');
}

// Exporter pour utilisation dans la console
if (typeof window !== 'undefined') {
    window.imageManagementTests = {
        runAllTests,
        testFileValidation,
        testPreviewUrlManagement,
        testMediaTypeDetection,
        testImageDeletion
    };
    
    console.log('📝 Tests disponibles dans window.imageManagementTests');
    console.log('   Utilisez: imageManagementTests.runAllTests()');
}

// Auto-exécution si dans Node.js
if (typeof module !== 'undefined') {
    runAllTests();
}
