# 📚 API Documentation - MbokaTour Likes & Favorites System

## 🌟 Overview

MbokaTour API provides comprehensive endpoints for managing user interactions with places through likes and favorites. This documentation covers all available endpoints, request/response formats, and authentication requirements.

**Base URL:** `https://api.mbokatour.com`
**Authentication:** Bearer Token (Laravel Sanctum)

---

## 🔐 Authentication

All protected endpoints require authentication using Bearer tokens:

```http
Authorization: Bearer {your-token}
```

### Get Authentication Token

**POST** `/api/auth/login`

```json
{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "user": {
    "id": 1,
    "name": "<PERSON>",
    "email": "<EMAIL>"
  },
  "token": "1|abc123..."
}
```

---

## 💖 Likes Endpoints

### Toggle Like/Unlike

**POST** `/api/places/likes/toggle`

Toggle like status for a place. If user hasn't liked the place, it will be liked. If already liked, it will be unliked.

**Headers:**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "place_id": 123
}
```

**Response (200):**
```json
{
  "message": "Lieu liké avec succès.",
  "is_liked": true,
  "likes_count": 42
}
```

**Response (Unlike):**
```json
{
  "message": "Like retiré avec succès.",
  "is_liked": false,
  "likes_count": 41
}
```

**Error Responses:**
- `401 Unauthorized` - Missing or invalid token
- `422 Validation Error` - Invalid place_id
- `404 Not Found` - Place doesn't exist

---

### Get Like Status

**GET** `/api/places/{place_id}/likes/status`

Get the current like status and count for a specific place.

**Headers:**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "is_liked": true,
  "likes_count": 42
}
```

---

### Get Like Statistics

**GET** `/api/places/{place_id}/likes/stats`

Get detailed like statistics including recent users who liked the place.

**Headers:**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "likes_count": 42,
  "recent_likes": [
    {
      "user": {
        "id": 1,
        "name": "John Doe"
      },
      "liked_at": "2025-06-16T10:30:00Z"
    },
    {
      "user": {
        "id": 2,
        "name": "Jane Smith"
      },
      "liked_at": "2025-06-16T09:15:00Z"
    }
  ]
}
```

---

### Get User's Liked Places

**GET** `/api/user/likes/places`

Get all places liked by the authenticated user.

**Headers:**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
[
  {
    "id": 123,
    "name": "Marché Central",
    "description": "Le plus grand marché de Kinshasa...",
    "location": "Kinshasa, RDC",
    "price": 0.00,
    "is_free": true,
    "main_image_url": "https://api.mbokatour.com/storage/places/main/image.jpg",
    "categories": [
      {
        "id": 1,
        "name": "Shopping",
        "icon": "shopping-bag",
        "color": "#fcc804"
      }
    ],
    "images": [
      {
        "id": 1,
        "image_url": "https://api.mbokatour.com/storage/places/images/image1.jpg"
      }
    ]
  }
]
```

---

## ⭐ Favorites Endpoints

### Get User's Favorite Places

**GET** `/api/user/favorites/places`

Get all places favorited by the authenticated user.

**Headers:**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
[
  {
    "id": 456,
    "name": "Musée National",
    "description": "Découvrez l'histoire du Congo...",
    "location": "Kinshasa, RDC",
    "price": 5.00,
    "is_free": false,
    "main_image_url": "https://api.mbokatour.com/storage/places/main/museum.jpg",
    "categories": [...],
    "images": [...]
  }
]
```

---

### Add to Favorites

**POST** `/api/user/favorites/places`

Add a place to user's favorites.

**Headers:**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "place_id": 456
}
```

**Response (200):**
```json
{
  "message": "Place added to favorites."
}
```

---

### Remove from Favorites

**DELETE** `/api/user/favorites/places/{place_id}`

Remove a place from user's favorites.

**Headers:**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "message": "Place removed from favorites."
}
```

---

## 🏛️ Places Endpoints (Enhanced with Likes/Favorites)

### Discover Places

**GET** `/api/places/discover`

Get a curated list of places with like and favorite information.

**Query Parameters:**
- `limit` (optional): Number of results (max: 50, default: 20)
- `category_id` (optional): Filter by category ID

**Headers (optional):**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
[
  {
    "id": 123,
    "name": "Marché Central",
    "description": "Le plus grand marché de Kinshasa...",
    "location": "Kinshasa, RDC",
    "price": 0.00,
    "is_free": true,
    "latitude": -4.3317,
    "longitude": 15.3139,
    "address": "Avenue Kabasele Tshamala",
    "neighborhood": "Kinshasa",
    "city": "Kinshasa",
    "main_image_url": "https://api.mbokatour.com/storage/places/main/image.jpg",
    "mediaType": "image",
    "is_featured": true,
    "views_count": 1250,
    "priority": 5,
    "is_favorited": true,
    "is_liked": false,
    "likes_count": 42,
    "categories": [
      {
        "id": 1,
        "name": "Shopping",
        "icon": "shopping-bag",
        "color": "#fcc804"
      }
    ],
    "images": [
      {
        "id": 1,
        "place_id": 123,
        "image_url": "https://api.mbokatour.com/storage/places/images/image1.jpg"
      }
    ]
  }
]
```

**Note:** For unauthenticated users, `is_favorited` and `is_liked` will always be `false`.

---

### Get Place Details

**GET** `/api/places/{id}`

Get detailed information about a specific place, including likes, favorites, and comments.

**Headers (optional):**
```http
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "id": 123,
  "name": "Marché Central",
  "description": "Le plus grand marché de Kinshasa...",
  "location": "Kinshasa, RDC",
  "price": 0.00,
  "is_free": true,
  "latitude": -4.3317,
  "longitude": 15.3139,
  "address": "Avenue Kabasele Tshamala",
  "neighborhood": "Kinshasa",
  "city": "Kinshasa",
  "opening_hours": "06:00 - 18:00",
  "status": "active",
  "main_image_url": "https://api.mbokatour.com/storage/places/main/image.jpg",
  "mediaType": "image",
  "is_active": true,
  "is_featured": true,
  "views_count": 1251,
  "priority": 5,
  "is_favorited": true,
  "is_liked": false,
  "likes_count": 42,
  "created_at": "2025-06-16T10:00:00Z",
  "updated_at": "2025-06-16T15:30:00Z",
  "categories": [...],
  "images": [...],
  "comments": [
    {
      "id": 1,
      "content": "Excellent endroit pour faire ses courses!",
      "rating": 5,
      "created_at": "2025-06-16T14:00:00Z",
      "user": {
        "id": 1,
        "name": "John Doe"
      }
    }
  ]
}
```

---

## 🔍 Search Places

**GET** `/api/search`

Search for places by name, description, or neighborhood.

**Query Parameters:**
- `query` (required): Search term

**Response (200):**
```json
[
  {
    "id": 123,
    "name": "Marché Central",
    "description": "Le plus grand marché de Kinshasa...",
    "location": "Kinshasa, RDC",
    "main_image_url": "https://api.mbokatour.com/storage/places/main/image.jpg",
    "is_favorited": false,
    "is_liked": false,
    "likes_count": 42
  }
]
```

---

## 📊 Data Models

### Place Object
```json
{
  "id": "integer",
  "name": "string",
  "description": "string",
  "location": "string",
  "price": "decimal",
  "is_free": "boolean",
  "latitude": "decimal",
  "longitude": "decimal",
  "address": "string",
  "neighborhood": "string",
  "city": "string",
  "opening_hours": "string",
  "status": "string",
  "main_image_url": "string",
  "mediaType": "string",
  "is_active": "boolean",
  "is_featured": "boolean",
  "views_count": "integer",
  "priority": "integer",
  "is_favorited": "boolean",
  "is_liked": "boolean",
  "likes_count": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Category Object
```json
{
  "id": "integer",
  "name": "string",
  "icon": "string",
  "color": "string"
}
```

### User Object
```json
{
  "id": "integer",
  "name": "string",
  "email": "string"
}
```

---

## ⚠️ Error Responses

### Standard Error Format
```json
{
  "message": "Error description",
  "errors": {
    "field_name": [
      "Validation error message"
    ]
  }
}
```

### Common HTTP Status Codes
- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request format
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `422 Unprocessable Entity` - Validation errors
- `500 Internal Server Error` - Server error

---

## 🎨 MbokaTour Color Palette

The API responses include color codes that match MbokaTour's brand:

- **Primary Yellow:** `#fcc804` (500)
- **Light Yellow:** `#fffeea` (50)
- **Dark Yellow:** `#472401` (950)

---

## 📱 Usage Examples

### JavaScript/React Native Example

```javascript
// Toggle like
const toggleLike = async (placeId) => {
  try {
    const response = await fetch('/api/places/likes/toggle', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({ place_id: placeId })
    });

    const data = await response.json();
    console.log(data.message, data.is_liked, data.likes_count);
    return data;
  } catch (error) {
    console.error('Error toggling like:', error);
    throw error;
  }
};

// Add to favorites
const addToFavorites = async (placeId) => {
  try {
    const response = await fetch('/api/user/favorites/places', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${userToken}`
      },
      body: JSON.stringify({ place_id: placeId })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error adding to favorites:', error);
    throw error;
  }
};

// Get places with likes/favorites
const getPlaces = async (limit = 20, categoryId = null) => {
  try {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit);
    if (categoryId) params.append('category_id', categoryId);

    const response = await fetch(`/api/places/discover?${params}`, {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });

    const places = await response.json();
    return places;
  } catch (error) {
    console.error('Error fetching places:', error);
    throw error;
  }
};

// Get user's liked places
const getLikedPlaces = async () => {
  try {
    const response = await fetch('/api/user/likes/places', {
      headers: {
        'Authorization': `Bearer ${userToken}`
      }
    });

    const places = await response.json();
    return places;
  } catch (error) {
    console.error('Error fetching liked places:', error);
    throw error;
  }
};
```

### Flutter/Dart Example

```dart
import 'dart:convert';
import 'package:http/http.dart' as http;

class MbokaTourAPI {
  static const String baseUrl = 'https://api.mbokatour.com';
  final String token;

  MbokaTourAPI(this.token);

  // Toggle like
  Future<Map<String, dynamic>> toggleLike(int placeId) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/places/likes/toggle'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode({'place_id': placeId}),
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to toggle like');
    }
  }

  // Get places
  Future<List<dynamic>> getPlaces({int limit = 20, int? categoryId}) async {
    String url = '$baseUrl/api/places/discover?limit=$limit';
    if (categoryId != null) {
      url += '&category_id=$categoryId';
    }

    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    } else {
      throw Exception('Failed to load places');
    }
  }
}
```

---

## 🎯 Likes vs Favorites - Key Differences

| Aspect | Likes 💖 | Favoris ⭐ |
|--------|----------|-----------|
| **Purpose** | Quick appreciation | Save for later |
| **Visibility** | Public (counter visible) | Private (personal list) |
| **Action** | Toggle (like/unlike) | Add/Remove |
| **Frequency** | High (frequent action) | Low (thoughtful action) |
| **UI Element** | Heart button | Star/Bookmark button |
| **Data Usage** | Analytics, popularity | Personal collection |
| **Social Aspect** | Shows appreciation | Personal organization |

---

## 💡 Best Practices

### For Frontend Developers

1. **Optimistic Updates**: Update UI immediately, then sync with server
2. **Error Handling**: Always handle network errors gracefully
3. **Loading States**: Show loading indicators during API calls
4. **Debouncing**: Prevent rapid-fire like/unlike actions
5. **Offline Support**: Cache like states for offline viewing

### Example Implementation

```javascript
// Optimistic update example
const handleLikeToggle = async (placeId) => {
  // Optimistic update
  setIsLiked(!isLiked);
  setLikesCount(isLiked ? likesCount - 1 : likesCount + 1);

  try {
    const result = await toggleLike(placeId);
    // Sync with server response
    setIsLiked(result.is_liked);
    setLikesCount(result.likes_count);
  } catch (error) {
    // Revert optimistic update on error
    setIsLiked(isLiked);
    setLikesCount(likesCount);
    showError('Failed to update like status');
  }
};
```

### For Backend Integration

1. **Caching**: Cache like counts for popular places
2. **Batch Operations**: Consider batch endpoints for multiple actions
3. **Analytics**: Track like patterns for recommendations
4. **Performance**: Use database indexes for efficient queries

---

## 🔄 Rate Limiting

API requests are rate-limited to ensure fair usage:
- **Authenticated users:** 60 requests per minute
- **Guest users:** 30 requests per minute

### Rate Limit Headers

All responses include rate limit information:

```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 59
X-RateLimit-Reset: 1640995200
```

---

## � Events & Webhooks (Future Feature)

*Note: Webhooks are planned for future releases*

### Planned Events

- `place.liked` - When a place receives a like
- `place.unliked` - When a like is removed
- `place.favorited` - When a place is added to favorites
- `place.unfavorited` - When a place is removed from favorites

---

## ❓ Frequently Asked Questions

### Q: Can a user like and favorite the same place?
**A:** Yes, likes and favorites are independent. A user can like a place for quick appreciation and also add it to favorites for later reference.

### Q: What happens to likes when a place is deleted?
**A:** All likes are automatically deleted due to cascade deletion constraints in the database.

### Q: Is there a limit to how many places a user can like or favorite?
**A:** Currently, there are no limits. Users can like and favorite as many places as they want.

### Q: Can I get a list of users who liked a specific place?
**A:** Yes, use the `/api/places/{place_id}/likes/stats` endpoint which returns recent likes with user information (limited to 10 most recent).

### Q: How are like counts calculated?
**A:** Like counts are calculated in real-time from the database. For high-traffic applications, consider implementing caching.

### Q: Can guest users see like counts?
**A:** Yes, like counts are public information. However, guest users cannot see which specific users liked a place, and their `is_liked` status will always be `false`.

### Q: What's the difference between this API and social media likes?
**A:** MbokaTour likes are specifically for places and tourism content. They're designed to help users discover popular destinations and express appreciation for places they've visited or want to visit.

---

## 🔧 Troubleshooting

### Common Issues

**401 Unauthorized**
- Check if your Bearer token is valid
- Ensure the token is properly formatted: `Bearer {token}`
- Verify the token hasn't expired

**422 Validation Error**
- Verify `place_id` exists in the database
- Check request body format matches the documentation
- Ensure all required fields are provided

**429 Too Many Requests**
- You've exceeded the rate limit
- Wait for the rate limit to reset (check `X-RateLimit-Reset` header)
- Implement proper rate limiting in your client

**500 Internal Server Error**
- Server-side issue
- Contact support with the timestamp and request details

---

## �📞 Support

For API support and questions:
- **Email:** <EMAIL>
- **Documentation:** [docs.mbokatour.com](https://docs.mbokatour.com)
- **Status Page:** [status.mbokatour.com](https://status.mbokatour.com)
- **GitHub Issues:** [github.com/mbokatour/api-issues](https://github.com/mbokatour/api-issues)

---

## 📋 Changelog

### v1.0.0 (June 16, 2025)
- ✅ Initial release of Likes & Favorites API
- ✅ Complete CRUD operations for likes and favorites
- ✅ Integration with existing places endpoints
- ✅ Comprehensive documentation and examples
- ✅ Rate limiting implementation
- ✅ Full test coverage

---

*Last updated: June 16, 2025*
