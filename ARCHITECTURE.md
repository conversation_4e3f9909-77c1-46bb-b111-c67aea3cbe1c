# Architecture Laravel : Controller → Service → Repository

## 🧠 Objectif
Cette architecture permet de séparer clairement :
- La logique de **contrôle** (Controller)
- La logique **métier** (Service)
- La logique d’**accès aux données** (Repository)

Cela rend l'application plus **maintenable**, **testable** et **évolutive**.

---

## 🧱 1. Controller (HTTP Layer)

### Rôle :
- Réception des requêtes HTTP
- Validation des données d’entrée
- Appel aux services
- Retour des réponses (JSON, Vue…)

### Exemple :
```php
public function store(UserRegisterRequest $request, UserService $service) {
    $user = $service->registerUser($request->validated());
    return response()->json($user);
}


 ### structure de dossier 

app/
├── Http/
│   └── Controllers/
│       └── Api/
│           └── UserController.php
├── Services/
│   └── UserService.php
├── Repositories/
│   ├── Interfaces/
│   │   └── UserRepositoryInterface.php
│   └── UserRepository.php


# Flux complet

[Requête HTTP]
     ↓
[Controller]
     ↓
[Service]
     ↓
[Repository]
     ↓
[Base de Données]



