# Améliorations de la méthode `getDiscoverPlaces`

## Résumé des améliorations

La méthode `getDiscoverPlaces` du `PlaceRepository` a été considérablement améliorée pour offrir de meilleures performances, plus de flexibilité et une meilleure expérience utilisateur.

## Problèmes identifiés dans l'ancienne version

1. **Performance** : Récupération de tous les lieux actifs sans pagination
2. **Optimisation des requêtes** : Relations manquantes (images) et sélection de tous les champs
3. **Logique de tri** : Ordre aléatoire simple après tri par `is_featured`
4. **Flexibilité** : Aucun paramètre pour filtrer ou limiter les résultats
5. **Expérience utilisateur** : Pas de support pour les favoris utilisateur

## Nouvelles fonctionnalités

### 1. Paramètres optionnels
```php
$options = [
    'limit' => 20,           // Limite le nombre de résultats (défaut: 20, max: 50)
    'category_id' => 1,      // Filtre par catégorie
    'user_id' => 123         // ID utilisateur pour les favoris
];
$places = $repository->getDiscoverPlaces($options);
```

### 2. Optimisation des performances
- **Sélection de champs spécifiques** : Seuls les champs nécessaires sont récupérés
- **Relations optimisées** : Chargement eager des catégories et images avec sélection de champs
- **Limitation par défaut** : 20 résultats maximum par défaut

### 3. Logique de tri améliorée
1. **Lieux mis en avant** (`is_featured = true`) en premier
2. **Priorité** (`priority`) en ordre décroissant
3. **Popularité** (`views_count`) en ordre décroissant
4. **Ordre aléatoire** pour la diversité

### 4. Support des favoris utilisateur
- Ajout automatique du champ `is_favorited` si un `user_id` est fourni
- Optimisation de la requête pour éviter les N+1 queries

### 5. Filtrage par catégorie
- Possibilité de filtrer les lieux par catégorie spécifique
- Utilisation de `whereHas` pour une requête optimisée

## Utilisation dans l'API

### Endpoint : `GET /api/places/discover`

#### Paramètres de requête
- `limit` (optionnel) : Nombre de résultats (1-50, défaut: 20)
- `category_id` (optionnel) : ID de la catégorie pour filtrer

#### Exemples d'utilisation
```bash
# Récupérer 10 lieux
GET /api/places/discover?limit=10

# Filtrer par catégorie
GET /api/places/discover?category_id=6

# Combinaison
GET /api/places/discover?limit=5&category_id=6
```

#### Réponse JSON
```json
[
  {
    "id": 5,
    "name": "Restaurant Chez Maman Colonelle",
    "description": "Savourez la cuisine congolaise authentique...",
    "location": "Matonge, Kinshasa",
    "price": 15,
    "is_free": false,
    "latitude": -4.35,
    "longitude": 15.32,
    "address": "Avenue Tombalbaye",
    "neighborhood": "Matonge",
    "city": "Kinshasa",
    "main_image_url": "http://example.com/storage/image.jpg",
    "mediaType": "image",
    "is_featured": true,
    "views_count": 347,
    "priority": 2,
    "is_favorited": false,
    "categories": [
      {
        "id": 6,
        "name": "Restaurants & Gastronomie",
        "icon": null,
        "color": null
      }
    ],
    "images": []
  }
]
```

## Tests

Des tests complets ont été ajoutés pour vérifier :
- Le filtrage des lieux actifs uniquement
- Le respect de la limite de résultats
- Le filtrage par catégorie
- Le support des favoris utilisateur
- La logique de tri
- Le chargement des relations
- L'optimisation des champs sélectionnés

### Exécution des tests
```bash
# Tests du repository
php artisan test tests/Feature/PlaceRepositoryTest.php

# Tests de l'API
php artisan test tests/Feature/Api/PlaceControllerTest.php
```

## Impact sur les performances

### Avant
- Récupération de tous les champs de tous les lieux actifs
- Pas de limite, potentiellement des milliers d'enregistrements
- Relations non optimisées

### Après
- Sélection de 15 champs spécifiques seulement
- Limite par défaut de 20 résultats
- Relations optimisées avec sélection de champs
- Requêtes plus rapides et moins de mémoire utilisée

## Compatibilité

Les changements sont **rétrocompatibles** :
- L'ancienne signature `getDiscoverPlaces()` fonctionne toujours
- Les paramètres sont optionnels avec des valeurs par défaut sensées
- L'API existante continue de fonctionner sans modification

## Prochaines améliorations possibles

1. **Cache** : Mise en cache des résultats pour améliorer les performances
2. **Géolocalisation** : Tri par distance depuis la position de l'utilisateur
3. **Pagination** : Support de la pagination avec curseur
4. **Recherche** : Intégration de la recherche textuelle dans la découverte
5. **Personnalisation** : Recommandations basées sur l'historique utilisateur
