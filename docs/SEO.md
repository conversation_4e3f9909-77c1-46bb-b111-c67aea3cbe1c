# Configuration SEO - MbokaTour

## 📋 Vue d'ensemble

Cette documentation décrit la configuration SEO complète mise en place pour l'application MbokaTour, incluant les meta tags, Open Graph, Twitter Cards, JSON-LD et les optimisations techniques.

## 🛠️ Composants SEO

### 1. Package SEOTools Laravel

**Package installé :** `artesaos/seotools`

**Configuration :** `config/seotools.php`
- Meta tags par défaut
- Configuration Open Graph
- Twitter Cards
- JSON-LD structured data

### 2. Trait SEO

**Fichier :** `app/Traits/SEOTrait.php`

Méthodes disponibles :
- `configureSEO(array $config)` - Configuration générale
- `configureHomepageSEO()` - Configuration page d'accueil
- `configurePlaceSEO($place)` - Configuration pages de lieux

### 3. Contrôleurs SEO

**WelcomeController :** `app/Http/Controllers/WelcomeController.php`
- Gère le SEO de la page d'accueil

**SitemapController :** `app/Http/Controllers/SitemapController.php`
- Génère le sitemap.xml dynamiquement

### 4. Middleware SEO

**Fichier :** `app/Http/Middleware/SEOMiddleware.php`
- Ajoute des headers de sécurité
- Configure le cache pour les ressources statiques

### 5. Composable Vue

**Fichier :** `resources/js/composables/useSEO.ts`
- Gestion des meta tags côté client
- Génération de JSON-LD
- Configuration dynamique

## 🔧 Configuration

### Variables d'environnement

Ajoutez ces variables à votre fichier `.env` :

```env
# SEO Configuration
SEO_TOOLS_INERTIA=true
SEO_DEFAULT_TITLE="MbokaTour - Redécouvre Kinshasa autrement"
SEO_DEFAULT_DESCRIPTION="L'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi."
SEO_DEFAULT_KEYWORDS="Kinshasa,tourisme,Congo,RDC,voyage,culture,événements,lieux,découverte,guide,application mobile,bons plans"
SEO_TWITTER_SITE="@MbokaTour"
SEO_OG_IMAGE="/images/og-image.jpg"
```

### Meta Tags par défaut

- **Title :** MbokaTour - Redécouvre Kinshasa autrement
- **Description :** L'application qui te fait redécouvrir Kinshasa autrement...
- **Keywords :** Kinshasa, tourisme, Congo, RDC, voyage, culture...
- **Robots :** index,follow
- **Canonical :** URL actuelle

### Open Graph

- **Type :** website (homepage), place (lieux)
- **Site Name :** MbokaTour
- **Locale :** fr_FR
- **Image :** /images/og-image.jpg (par défaut)

### Twitter Cards

- **Card Type :** summary_large_image
- **Site :** @MbokaTour
- **Creator :** @MbokaTour

## 📁 Fichiers SEO

### robots.txt

**Localisation :** `public/robots.txt`

Configuration :
- Autorise l'indexation générale
- Bloque les zones admin et API
- Référence le sitemap
- Délai de crawl respectueux

### sitemap.xml

**Route :** `/sitemap.xml`
**Contrôleur :** `SitemapController`

Inclut :
- Page d'accueil
- Tous les lieux actifs
- Toutes les catégories
- Pages statiques importantes

## 🎯 Utilisation

### Dans un contrôleur

```php
use App\Traits\SEOTrait;

class PlaceController extends Controller
{
    use SEOTrait;

    public function show(Place $place)
    {
        $this->configurePlaceSEO($place);
        
        return Inertia::render('Places/Show', [
            'place' => $place
        ]);
    }
}
```

### Dans une page Vue

```vue
<script setup lang="ts">
import { useSEO } from '@/composables/useSEO';

const { generateMetaTags, generateJsonLd } = useSEO();

const seoConfig = {
    title: 'Titre personnalisé',
    description: 'Description personnalisée',
    image: '/images/custom-image.jpg'
};

const metaTags = generateMetaTags(seoConfig);
const jsonLd = generateJsonLd(seoConfig);
</script>

<template>
    <Head :title="metaTags.title">
        <meta v-for="meta in metaTags.meta" 
              :key="meta.name || meta.property" 
              v-bind="meta" />
        <link v-for="link in metaTags.link" 
              :key="link.rel" 
              v-bind="link" />
        <script type="application/ld+json" 
                v-html="JSON.stringify(jsonLd)">
        </script>
    </Head>
</template>
```

## 🚀 Optimisations techniques

### Performance

- Cache des ressources statiques (1 an)
- Compression gzip automatique
- Preconnect pour les polices
- Lazy loading des images

### Sécurité

- Headers de sécurité (X-Content-Type-Options, X-Frame-Options, etc.)
- Protection XSS
- Politique de référent stricte

### Accessibilité

- Meta tags pour les lecteurs d'écran
- Couleurs de thème définies
- Support des modes sombre/clair

## 📊 Monitoring SEO

### Outils recommandés

1. **Google Search Console**
   - Surveillance de l'indexation
   - Erreurs de crawl
   - Performance de recherche

2. **Google PageSpeed Insights**
   - Vitesse de chargement
   - Core Web Vitals
   - Optimisations suggérées

3. **Lighthouse**
   - Audit SEO complet
   - Performance
   - Accessibilité

### Métriques à surveiller

- Temps de chargement des pages
- Taux d'indexation
- Positions dans les résultats de recherche
- Clics organiques
- Core Web Vitals

## 🔄 Maintenance

### Tâches régulières

1. **Mise à jour du sitemap**
   - Automatique via le contrôleur
   - Vérification mensuelle

2. **Optimisation des images**
   - Compression des nouvelles images
   - Formats WebP/AVIF

3. **Audit des meta tags**
   - Vérification des doublons
   - Optimisation des descriptions

4. **Monitoring des performances**
   - Vérification des Core Web Vitals
   - Optimisation continue

## 📝 Bonnes pratiques

### Meta descriptions

- Longueur : 150-160 caractères
- Unique pour chaque page
- Inclut des mots-clés pertinents
- Appel à l'action clair

### Titres de page

- Longueur : 50-60 caractères
- Unique et descriptif
- Inclut le nom de la marque
- Hiérarchie logique (H1, H2, H3...)

### Images

- Alt text descriptif
- Noms de fichiers optimisés
- Tailles appropriées
- Formats modernes (WebP, AVIF)

### URLs

- Structure claire et logique
- Mots-clés dans l'URL
- Éviter les paramètres inutiles
- Redirections 301 pour les changements
