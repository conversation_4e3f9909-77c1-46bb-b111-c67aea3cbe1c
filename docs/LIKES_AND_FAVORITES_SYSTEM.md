# 💖 Système de Likes et Favoris - MbokaTour

## 📋 Vue d'ensemble

MbokaTour implémente deux systèmes distincts pour l'engagement des utilisateurs :

1. **💖 Likes** : Actions rapides d'appréciation (similaire à Instagram/Facebook)
2. **⭐ Favoris** : Sauvegarde de lieux pour consultation ultérieure (similaire à des bookmarks)

## 🏗️ Architecture

### Tables de base de données

#### `place_likes`
```sql
- id (BIGINT, PK)
- user_id (BIGINT, FK vers users)
- place_id (BIGINT, FK vers places)
- created_at, updated_at
- UNIQUE(user_id, place_id) -- Un utilisateur ne peut liker qu'une fois
```

#### `user_place_favorites` (existante)
```sql
- id (BIGINT, PK)
- user_id (BIGINT, FK vers users)
- place_id (BIGINT, FK vers places)
- created_at, updated_at
```

### Modèles Eloquent

#### PlaceLike
- Relations : `user()`, `place()`
- Fillable : `user_id`, `place_id`

#### Place (mis à jour)
- Nouvelles relations : `likes()`, `likedBy()`
- Relations existantes : `favoritedBy()`, `comments()`, etc.

#### User (mis à jour)
- Nouvelles relations : `placeLikes()`, `likedPlaces()`
- Relations existantes : `favoritePlaces()`, `comments()`, etc.

## 🚀 API Endpoints

### Likes

#### POST `/api/places/likes/toggle`
**Authentification requise**
```json
{
  "place_id": 123
}
```
**Réponse :**
```json
{
  "message": "Lieu liké avec succès.",
  "is_liked": true,
  "likes_count": 42
}
```

#### GET `/api/places/{place_id}/likes/status`
**Authentification requise**
```json
{
  "is_liked": true,
  "likes_count": 42
}
```

#### GET `/api/places/{place_id}/likes/stats`
**Authentification requise**
```json
{
  "likes_count": 42,
  "recent_likes": [
    {
      "user": {"id": 1, "name": "John Doe"},
      "liked_at": "2025-06-16T10:30:00Z"
    }
  ]
}
```

#### GET `/api/user/likes/places`
**Authentification requise**
```json
[
  {
    "id": 123,
    "name": "Lieu Example",
    "description": "...",
    "categories": [...],
    "images": [...]
  }
]
```

### Favoris (existants)

#### GET `/api/user/favorites/places`
#### POST `/api/user/favorites/places`
#### DELETE `/api/user/favorites/places/{place_id}`

## 📊 Intégration dans les réponses API

### GET `/api/places/discover`
```json
[
  {
    "id": 123,
    "name": "Lieu Example",
    "is_favorited": true,
    "is_liked": false,
    "likes_count": 42,
    "categories": [...],
    "images": [...]
  }
]
```

### GET `/api/places/{id}`
```json
{
  "id": 123,
  "name": "Lieu Example",
  "is_favorited": true,
  "is_liked": false,
  "likes_count": 42,
  "categories": [...],
  "images": [...],
  "comments": [...]
}
```

## 🔧 Utilisation côté client

### Liker/Unliker un lieu
```javascript
const toggleLike = async (placeId) => {
  const response = await fetch('/api/places/likes/toggle', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ place_id: placeId })
  });
  
  const data = await response.json();
  console.log(data.message, data.is_liked, data.likes_count);
};
```

### Ajouter aux favoris
```javascript
const addToFavorites = async (placeId) => {
  const response = await fetch('/api/user/favorites/places', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({ place_id: placeId })
  });
};
```

## 🎯 Différences conceptuelles

| Aspect | Likes 💖 | Favoris ⭐ |
|--------|----------|-----------|
| **Usage** | Appréciation rapide | Sauvegarde pour plus tard |
| **Visibilité** | Publique (compteur) | Privée (liste personnelle) |
| **Fréquence** | Action fréquente | Action réfléchie |
| **Interface** | Bouton cœur | Bouton étoile/bookmark |
| **Données** | Compteur + statut | Liste personnelle |

## 🔒 Sécurité

- Toutes les actions nécessitent une authentification
- Contrainte unique empêche les doublons
- Validation des IDs de lieux
- Suppression en cascade lors de la suppression d'un lieu/utilisateur

## 📈 Optimisations

- Index sur `(place_id, created_at)` pour les requêtes de comptage
- Index sur `(user_id, created_at)` pour les listes utilisateur
- Chargement eager des relations dans les repositories
- Sélection de champs spécifiques pour les performances

## 🧪 Tests

Des factories sont disponibles pour les tests :
- `PlaceLikeFactory`
- Tests d'intégration dans `PlaceRepositoryTest`
- Tests API dans `PlaceControllerTest`
