# Correction du problème de migration - MbokaTour

## 🚨 Problème rencontré

```
SQLSTATE[HY000]: General error: 1005 Can't create table `events` 
(errno: 150 "Foreign key constraint is incorrectly formed")
```

**Cause :** La migration des événements essaie de créer une clé étrangère vers la table `categories` qui n'existe pas encore.

## 🔧 Solution appliquée

### 1. Migrations créées/corrigées

#### ✅ Nouvelles migrations ajoutées :
- `2025_06_07_081300_create_categories_table.php` - Table des catégories
- `2025_06_07_081310_create_category_place_table.php` - Table pivot places-catégories  
- `2025_06_07_081320_create_place_likes_table.php` - Table des likes
- `2025_06_07_081325_create_user_place_favorites_table.php` - Table des favoris
- `2025_06_07_081330_create_comments_table.php` - Table des commentaires

#### ✅ Migrations corrigées :
- `2025_06_07_081229_create_places_table.php` - Utilise UUID dès le départ
- `2025_06_07_081244_create_place_images_table.php` - Clé étrangère UUID

### 2. Ordre des migrations

Les migrations s'exécutent maintenant dans le bon ordre :
1. `users` (Laravel)
2. `cache` (Laravel) 
3. `jobs` (Laravel)
4. `places` (UUID)
5. `place_images` (UUID FK)
6. `categories`
7. `category_place` (pivot)
8. `events` (peut maintenant référencer categories)
9. `place_likes`
10. `user_place_favorites`
11. `comments`
12. `permission_tables` (Spatie)

## 🚀 Instructions pour le serveur de production

### Option 1: Réinitialisation complète (RECOMMANDÉE)

Si vous pouvez perdre les données existantes :

```bash
# 1. Sauvegarder les données importantes (optionnel)
php artisan db:seed --class=BackupSeeder

# 2. Réinitialiser complètement
./reset-database.sh

# 3. Ou manuellement :
php artisan db:wipe --force
php artisan migrate --force
php artisan db:seed --force
```

### Option 2: Migration progressive (si vous avez des données à conserver)

```bash
# 1. Rollback jusqu'avant le problème
php artisan migrate:rollback --step=10

# 2. Supprimer les tables problématiques manuellement
mysql -u username -p database_name << EOF
DROP TABLE IF EXISTS events;
DROP TABLE IF EXISTS place_images;
DROP TABLE IF EXISTS places;
EOF

# 3. Relancer les migrations
php artisan migrate --force

# 4. Restaurer les données sauvegardées
php artisan db:seed --class=RestoreSeeder
```

### Option 3: Correction manuelle de la base de données

```sql
-- 1. Créer la table categories d'abord
CREATE TABLE categories (
    id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(255) NOT NULL,
    description TEXT NULL,
    icon VARCHAR(255) NULL,
    color VARCHAR(255) NOT NULL DEFAULT '#fcc804',
    display_order INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    INDEX idx_type_display_order (type, display_order),
    INDEX idx_type (type)
);

-- 2. Modifier la table places pour UUID (si elle existe)
ALTER TABLE places 
MODIFY COLUMN id CHAR(36) NOT NULL,
DROP PRIMARY KEY,
ADD PRIMARY KEY (id);

-- 3. Continuer avec les migrations
```

## 📊 Structure finale des tables

### Table `places` (UUID)
```sql
id: UUID (primary key)
name: VARCHAR(255)
description: TEXT
location: VARCHAR(255)
price: DECIMAL(8,2) NULL
is_free: BOOLEAN DEFAULT false
latitude: DECIMAL(10,8) NULL
longitude: DECIMAL(11,8) NULL
address: VARCHAR(255) NULL
neighborhood: VARCHAR(255) NULL
city: VARCHAR(255) NULL
opening_hours: TEXT NULL
status: VARCHAR(255) DEFAULT 'active'
main_image_url: VARCHAR(255) NULL
mediaType: VARCHAR(255) NULL
is_active: BOOLEAN DEFAULT true
is_featured: BOOLEAN DEFAULT false
views_count: INT DEFAULT 0
priority: INT DEFAULT 0
created_at: TIMESTAMP
updated_at: TIMESTAMP
```

### Table `categories`
```sql
id: BIGINT UNSIGNED (primary key)
name: VARCHAR(255)
type: VARCHAR(255) ('place' ou 'event')
description: TEXT NULL
icon: VARCHAR(255) NULL
color: VARCHAR(255) DEFAULT '#fcc804'
display_order: INT DEFAULT 0
created_at: TIMESTAMP
updated_at: TIMESTAMP
```

### Relations avec UUID
- `place_images.place_id` → `places.id` (UUID)
- `category_place.place_id` → `places.id` (UUID)
- `place_likes.place_id` → `places.id` (UUID)
- `user_place_favorites.place_id` → `places.id` (UUID)
- `comments.place_id` → `places.id` (UUID)

## 🔍 Vérifications post-migration

```bash
# Vérifier que toutes les migrations sont appliquées
php artisan migrate:status

# Vérifier les tables créées
php artisan tinker --execute="
echo 'Tables: ' . implode(', ', Schema::getTableListing()) . PHP_EOL;
echo 'Places count: ' . App\Models\Place::count() . PHP_EOL;
echo 'Categories count: ' . App\Models\Category::count() . PHP_EOL;
"

# Tester la création d'une place
php artisan tinker --execute="
\$place = App\Models\Place::create([
    'name' => 'Test Place',
    'description' => 'Test description',
    'location' => 'Kinshasa'
]);
echo 'Place créée avec ID: ' . \$place->id . PHP_EOL;
\$place->delete();
echo 'Test réussi!' . PHP_EOL;
"
```

## 🛠️ Scripts utiles

### Sauvegarde avant migration
```bash
# Exporter les données existantes
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Ou avec Laravel
php artisan db:seed --class=BackupSeeder
```

### Restauration en cas de problème
```bash
# Restaurer depuis sauvegarde SQL
mysql -u username -p database_name < backup_file.sql

# Ou réinitialiser complètement
./reset-database.sh
```

## 📞 Support

Si vous rencontrez encore des problèmes :

1. **Vérifiez les logs** : `tail -f storage/logs/laravel.log`
2. **Vérifiez MySQL** : `SHOW ENGINE INNODB STATUS;`
3. **Permissions** : Vérifiez que l'utilisateur MySQL a les droits CREATE, ALTER, DROP
4. **Version MySQL** : Assurez-vous d'utiliser MySQL 5.7+ ou MariaDB 10.2+

## ✅ Résultat attendu

Après correction, vous devriez avoir :
- ✅ Toutes les migrations appliquées sans erreur
- ✅ Tables avec UUID pour les places
- ✅ Relations fonctionnelles
- ✅ Seeders exécutés avec succès
- ✅ Application fonctionnelle

---

**Dernière mise à jour :** 18 juin 2025
