# Gestion des Fichiers des Places

Ce document explique comment la gestion des fichiers (images et vidéos) fonctionne pour les places dans MbokaTour.

## 🎯 Fonctionnalités Implémentées

### 1. Suppression Automatique lors de la Suppression d'une Place

Quand une place est supprimée, **tous les fichiers associés sont automatiquement supprimés** :

- ✅ **Image/vidéo principale** (`main_image_url`)
- ✅ **Toutes les images de la galerie** (table `place_images`)
- ✅ **Suppression des fichiers physiques** du stockage
- ✅ **Suppression des enregistrements** de la base de données

### 2. Double Protection

La suppression des fichiers est implémentée à deux niveaux :

#### A. Dans le Service (`PlaceService`)
```php
public function deletePlace(string $id)
{
    $place = $this->placeRepository->findById($id);
    
    // Supprimer l'image principale
    $this->deleteMainImageFile($place);
    
    // Supprimer toutes les images associées
    $this->deletePlaceImages($place);
    
    // Supprimer la place de la base de données
    return $this->placeRepository->delete($id);
}
```

#### B. Dans le Modèle (`Place::boot()`)
```php
static::deleting(function ($place) {
    // Supprimer l'image principale
    $mainImagePath = $place->getRawMainImagePath();
    if ($mainImagePath && Storage::disk('public')->exists($mainImagePath)) {
        Storage::disk('public')->delete($mainImagePath);
    }

    // Supprimer toutes les images associées
    foreach ($place->images as $image) {
        $imagePath = $image->getRawImagePath();
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            Storage::disk('public')->delete($imagePath);
        }
    }
});
```

### 3. Gestion des URLs Externes

Le système distingue intelligemment entre :
- **Fichiers locaux** : Stockés dans `storage/app/public/places/`
- **URLs externes** : Comme Unsplash, qui ne doivent pas être supprimées

```php
// Vérification avant suppression
if ($imagePath && !filter_var($imagePath, FILTER_VALIDATE_URL)) {
    Storage::disk('public')->delete($imagePath);
}
```

### 4. Contraintes de Base de Données

La migration `place_images` utilise `onDelete('cascade')` :

```php
$table->foreignId('place_id')->constrained()->onDelete('cascade');
```

Cela garantit que les enregistrements `place_images` sont automatiquement supprimés quand une place est supprimée.

## 🧹 Commande de Nettoyage

Une commande Artisan est disponible pour nettoyer les fichiers orphelins :

### Aperçu (Dry Run)
```bash
php artisan places:clean-orphaned-files --dry-run
```

### Nettoyage Réel
```bash
php artisan places:clean-orphaned-files
```

Cette commande :
- ✅ Identifie les fichiers dans `places/main/` et `places/images/` qui ne sont plus référencés
- ✅ Ignore les URLs externes
- ✅ Affiche un rapport détaillé
- ✅ Permet un aperçu avec `--dry-run`

## 🧪 Tests

Des tests complets vérifient le bon fonctionnement :

```bash
php artisan test tests/Feature/PlaceDeleteTest.php
```

Les tests couvrent :
- ✅ Suppression d'une place avec images locales
- ✅ Suppression d'une place sans images
- ✅ Suppression d'une place avec URLs externes
- ✅ Vérification que tous les fichiers sont supprimés
- ✅ Vérification que les enregistrements DB sont supprimés

## 📁 Structure des Fichiers

```
storage/app/public/places/
├── main/           # Images/vidéos principales
│   ├── image1.jpg
│   ├── video1.mp4
│   └── ...
└── images/         # Images de galerie
    ├── gallery1.jpg
    ├── gallery2.mp4
    └── ...
```

## 🔧 Maintenance

### Surveillance des Fichiers Orphelins

Il est recommandé d'exécuter périodiquement la commande de nettoyage :

```bash
# Vérification hebdomadaire (cron)
0 2 * * 0 cd /path/to/project && php artisan places:clean-orphaned-files
```

### Logs

Toutes les suppressions sont loggées :

```php
Log::info("Deleted main image file: {$mainImagePath}");
Log::info("Deleted place image file: {$imagePath}");
```

## ⚠️ Points d'Attention

1. **Sauvegarde** : Assurez-vous d'avoir des sauvegardes avant de supprimer des places importantes
2. **URLs Externes** : Les images Unsplash et autres URLs externes ne sont jamais supprimées
3. **Permissions** : Vérifiez que Laravel a les permissions d'écriture sur `storage/app/public/`
4. **Espace Disque** : La commande de nettoyage aide à libérer l'espace disque

## 🚀 Utilisation

### Suppression d'une Place (Interface Admin)

1. Aller dans l'interface d'administration
2. Sélectionner la place à supprimer
3. Confirmer la suppression
4. ✅ Tous les fichiers sont automatiquement supprimés

### Suppression Programmatique

```php
use App\Services\PlaceService;

$placeService = app(PlaceService::class);
$placeService->deletePlace($placeId);
// Tous les fichiers sont automatiquement supprimés
```

Cette implémentation garantit une gestion propre et automatique des fichiers, évitant l'accumulation de fichiers orphelins dans le système.
