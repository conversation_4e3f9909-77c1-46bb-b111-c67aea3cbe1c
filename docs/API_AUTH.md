# API d'Authentification - MbokaTour

Cette documentation décrit les endpoints d'authentification de l'API MbokaTour.

## Base URL
```
http://localhost:8000/api
```

## Endpoints d'Authentification

### 1. Inscription (Register)

**POST** `/auth/register`

Crée un nouveau compte utilisateur.

#### Paramètres requis :
- `name` (string) : Nom de l'utilisateur
- `phone_number` (string) : <PERSON><PERSON><PERSON><PERSON> de téléphone unique
- `password` (string) : Mot de passe
- `password_confirmation` (string) : Confirmation du mot de passe

#### Paramètres optionnels :
- `email` (string) : Adresse email (nullable)

#### Exemple de requête :
```json
{
    "name": "<PERSON>",
    "phone_number": "+33123456789",
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123"
}
```

#### Réponse de succès (201) :
```json
{
    "message": "Utilisateur créé avec succès",
    "user": {
        "id": 1,
        "name": "<PERSON>",
        "phone_number": "+33123456789",
        "email": "<EMAIL>",
        "created_at": "2025-06-16T12:00:00.000000Z",
        "updated_at": "2025-06-16T12:00:00.000000Z"
    },
    "access_token": "1|abc123...",
    "token_type": "Bearer"
}
```

### 2. Connexion (Login)

**POST** `/auth/login`

Authentifie un utilisateur existant.

#### Paramètres requis :
- `login` (string) : Numéro de téléphone ou email
- `password` (string) : Mot de passe

#### Exemple de requête :
```json
{
    "login": "+33123456789",
    "password": "password123"
}
```

#### Réponse de succès (200) :
```json
{
    "message": "Connexion réussie",
    "user": {
        "id": 1,
        "name": "John Doe",
        "phone_number": "+33123456789",
        "email": "<EMAIL>"
    },
    "access_token": "2|def456...",
    "token_type": "Bearer"
}
```

### 3. Profil utilisateur (User Profile)

**GET** `/auth/user`

Récupère les informations de l'utilisateur connecté.

#### Headers requis :
```
Authorization: Bearer {token}
```

#### Réponse de succès (200) :
```json
{
    "user": {
        "id": 1,
        "name": "John Doe",
        "phone_number": "+33123456789",
        "email": "<EMAIL>",
        "created_at": "2025-06-16T12:00:00.000000Z",
        "updated_at": "2025-06-16T12:00:00.000000Z"
    }
}
```

### 4. Déconnexion (Logout)

**POST** `/auth/logout`

Déconnecte l'utilisateur et révoque le token actuel.

#### Headers requis :
```
Authorization: Bearer {token}
```

#### Réponse de succès (200) :
```json
{
    "message": "Déconnexion réussie"
}
```

### 5. Déconnexion de tous les appareils

**POST** `/auth/logout-all`

Déconnecte l'utilisateur de tous les appareils en révoquant tous ses tokens.

#### Headers requis :
```
Authorization: Bearer {token}
```

#### Réponse de succès (200) :
```json
{
    "message": "Déconnexion de tous les appareils réussie"
}
```

## Gestion des erreurs

### Erreurs de validation (422)
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "phone_number": [
            "The phone number field is required."
        ]
    }
}
```

### Erreurs d'authentification (401)
```json
{
    "message": "Unauthenticated."
}
```

### Erreurs de connexion (422)
```json
{
    "message": "The given data was invalid.",
    "errors": {
        "login": [
            "Les informations d'identification fournies sont incorrectes."
        ]
    }
}
```

## Notes importantes

1. **Numéro de téléphone** : Maintenant requis pour tous les nouveaux utilisateurs
2. **Email** : Optionnel, peut être null
3. **Authentification** : Utilise Laravel Sanctum pour la gestion des tokens
4. **Connexion flexible** : Les utilisateurs peuvent se connecter avec leur numéro de téléphone ou leur email
5. **Sécurité** : Tous les mots de passe sont hachés avec bcrypt
