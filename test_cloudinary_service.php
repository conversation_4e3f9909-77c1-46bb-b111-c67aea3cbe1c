<?php

/**
 * Test rapide du CloudinaryService
 */

require_once __DIR__ . '/vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use App\Services\CloudinaryService;

echo "=== Test du CloudinaryService ===\n\n";

try {
    // Créer une instance du service
    $cloudinaryService = new CloudinaryService();
    
    echo "✅ CloudinaryService créé avec succès\n";
    
    // Test de la configuration
    echo "🔧 Test de la configuration...\n";
    
    // Créer un fichier de test temporaire
    $testImagePath = tempnam(sys_get_temp_dir(), 'test_image') . '.jpg';
    
    // Créer une image de test simple (1x1 pixel)
    $imageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAHGbKdMDgAAAABJRU5ErkJggg==');
    file_put_contents($testImagePath, $imageData);
    
    // Créer un UploadedFile de test
    $uploadedFile = new \Illuminate\Http\UploadedFile(
        $testImagePath,
        'test.jpg',
        'image/jpeg',
        null,
        true
    );
    
    echo "📤 Test d'upload...\n";
    
    // Tester l'upload
    $result = $cloudinaryService->uploadFile($uploadedFile, 'mbokatour/test');
    
    echo "✅ Upload réussi !\n";
    echo "   Public ID: {$result['public_id']}\n";
    echo "   URL sécurisée: {$result['secure_url']}\n";
    echo "   Type de ressource: {$result['resource_type']}\n\n";
    
    // Test de suppression
    echo "🗑️  Test de suppression...\n";
    $deleteSuccess = $cloudinaryService->deleteFile($result['public_id'], $result['resource_type']);
    
    if ($deleteSuccess) {
        echo "✅ Suppression réussie !\n";
    } else {
        echo "⚠️  Suppression échouée\n";
    }
    
    // Nettoyer le fichier temporaire
    unlink($testImagePath);
    
    echo "\n🎉 Tous les tests sont passés ! Votre CloudinaryService fonctionne parfaitement.\n";
    echo "Vous pouvez maintenant utiliser l'interface d'administration pour uploader des images.\n";
    
} catch (Exception $e) {
    echo "❌ Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
    exit(1);
}
