# Système de Permissions MbokaTour

## Installation et Configuration

Le système de permissions utilise le package **<PERSON><PERSON>vel Permission** pour gérer les rôles et permissions.

### Packages installés
- `spatie/laravel-permission` - Gestion des rôles et permissions

### Migrations
Les tables suivantes ont été créées :
- `roles` - <PERSON><PERSON> les rôles (admin, user, etc.)
- `permissions` - Stocke les permissions (manage-places, manage-events, etc.)
- `model_has_permissions` - Relation many-to-many entre modèles et permissions
- `model_has_roles` - Relation many-to-many entre modèles et rôles
- `role_has_permissions` - Relation many-to-many entre rôles et permissions

## Rôles et Permissions

### Rôles disponibles
- **admin** - Administrateur avec tous les droits

### Permissions disponibles
- `manage-places` - Gestion des lieux
- `manage-events` - Gestion des événements
- `manage-categories` - Gestion des catégories
- `manage-users` - Gestion des utilisateurs

## Middlewares

### AdminMiddleware
Vérifie que l'utilisateur a le rôle `admin`.

```php
Route::middleware(['admin'])->group(function () {
    // Routes protégées par le rôle admin
});
```

### PermissionMiddleware
Vérifie que l'utilisateur a une permission spécifique.

```php
Route::middleware(['permission:manage-places'])->group(function () {
    // Routes protégées par la permission manage-places
});
```

## Utilisation dans les Routes

### Protection par rôle admin
```php
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Toutes les routes admin
});
```

### Protection par permissions spécifiques
```php
Route::middleware(['auth', 'verified', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    // Routes pour les places
    Route::middleware(['permission:manage-places'])->group(function () {
        Route::resource('places', PlaceController::class);
    });
    
    // Routes pour les événements
    Route::middleware(['permission:manage-events'])->group(function () {
        Route::resource('events', EventController::class);
    });
});
```

## Utilisation dans le Code

### Vérifier les rôles
```php
// Vérifier si l'utilisateur a un rôle
if (auth()->user()->hasRole('admin')) {
    // L'utilisateur est admin
}

// Vérifier plusieurs rôles
if (auth()->user()->hasAnyRole(['admin', 'moderator'])) {
    // L'utilisateur a au moins un de ces rôles
}
```

### Vérifier les permissions
```php
// Vérifier si l'utilisateur a une permission
if (auth()->user()->can('manage-places')) {
    // L'utilisateur peut gérer les places
}

// Vérifier plusieurs permissions
if (auth()->user()->hasAnyPermission(['manage-places', 'manage-events'])) {
    // L'utilisateur a au moins une de ces permissions
}
```

### Assigner des rôles et permissions
```php
// Assigner un rôle à un utilisateur
$user->assignRole('admin');

// Assigner une permission directement à un utilisateur
$user->givePermissionTo('manage-places');

// Assigner une permission à un rôle
$role = Role::findByName('admin');
$role->givePermissionTo('manage-places');
```

## Commandes Artisan

### Créer un utilisateur admin
```bash
php artisan admin:create-user
```

Ou avec des options :
```bash
php artisan admin:create-user --email=<EMAIL> --name="Admin User" --phone="+243123456789" --password=password123
```

### Seeder des rôles et permissions
```bash
php artisan db:seed --class=RolePermissionSeeder
```

## Utilisateur Admin par Défaut

Un utilisateur admin par défaut a été créé :
- **Email** : <EMAIL>
- **Mot de passe** : password123
- **Rôle** : admin
- **Permissions** : Toutes les permissions

## Utilisation dans les Vues (Blade/Inertia)

### Blade
```php
@can('manage-places')
    <a href="{{ route('admin.places.create') }}">Créer un lieu</a>
@endcan

@role('admin')
    <div>Contenu visible uniquement aux admins</div>
@endrole
```

### Inertia (côté serveur)
```php
// Dans un contrôleur
return Inertia::render('Dashboard', [
    'can' => [
        'manage_places' => auth()->user()->can('manage-places'),
        'manage_events' => auth()->user()->can('manage-events'),
    ],
    'is_admin' => auth()->user()->hasRole('admin'),
]);
```

## Sécurité

- Toutes les routes admin sont protégées par les middlewares `auth`, `verified` et `admin`
- Les permissions spécifiques ajoutent une couche de sécurité supplémentaire
- Les utilisateurs doivent être authentifiés et vérifiés pour accéder aux zones admin
- Le système utilise les Gates de Laravel pour une vérification cohérente

## Dépannage

### Erreur "Permission does not exist"
Assurez-vous que les permissions ont été créées via le seeder :
```bash
php artisan db:seed --class=RolePermissionSeeder
```

### Erreur "Role does not exist"
Vérifiez que les rôles ont été créés et que l'utilisateur a bien le rôle assigné.

### Cache des permissions
Si vous modifiez les permissions, videz le cache :
```bash
php artisan permission:cache-reset
```
