# Test de la correction du problème de suppression d'images

## Problème identifié
Quand on supprime une image de lieu (place image), l'image principale (main image) disparaît aussi de l'affichage.

## Cause du problème
1. **Variables réactives conflictuelles** : Les variables `mainImageStable` et `mediaTypeStable` n'étaient pas correctement isolées
2. **Gestion incohérente des états** : Confusion entre les variables locales et les variables "stables"
3. **Réactivité croisée** : La suppression d'une image de lieu affectait l'affichage de l'image principale

## Solution appliquée

### 1. Simplification des variables réactives
```javascript
// AVANT (problématique)
const localImages = ref([...props.place.images]);
const localMainImageUrl = ref(props.place.main_image_url);
const localMediaType = ref(props.place.mediaType);
const mainImageStable = ref(props.place.main_image_url);
const mediaTypeStable = ref(props.place.mediaType);

// APRÈS (corrigé)
const localImages = ref([...props.place.images]);
const currentMainImageUrl = ref(props.place.main_image_url);
const currentMediaType = ref(props.place.mediaType);
```

### 2. Amélioration de la fonction deleteImage
```javascript
// Ajout de commentaires explicites et de logs pour le debugging
async function deleteImage(imageId: number) {
    // ... code de suppression ...
    if (response.ok) {
        // Supprimer uniquement l'image de la liste des images de lieu
        // Sans affecter l'image principale
        const imageIndex = localImages.value.findIndex((img: any) => img.id === imageId);
        if (imageIndex > -1) {
            const newImages = [...localImages.value];
            newImages.splice(imageIndex, 1);
            localImages.value = newImages;
            
            console.log(`Image ${imageId} supprimée. Images restantes:`, localImages.value.length);
        }
    }
}
```

### 3. Correction du template
```vue
<!-- AVANT -->
<div v-if="form.main_image_url || mainImageStable" class="my-2">
    <div v-else-if="mainImageStable" class="relative">
        <video v-if="mediaTypeStable === 'video'" :src="mainImageStable">

<!-- APRÈS -->
<div v-if="form.main_image_url || currentMainImageUrl" class="my-2">
    <div v-else-if="currentMainImageUrl" class="relative">
        <p class="text-sm text-gray-600 mb-1">Image principale actuelle</p>
        <video v-if="currentMediaType === 'video'" :src="currentMainImageUrl">
```

## Changements clés

1. **Séparation claire** : L'image principale et les images de lieu sont maintenant complètement séparées
2. **Variables cohérentes** : Utilisation de `currentMainImageUrl` et `currentMediaType` partout
3. **Isolation des opérations** : La suppression d'images de lieu n'affecte que `localImages`
4. **Meilleur debugging** : Ajout de logs pour tracer les opérations

## Test à effectuer

1. Ouvrir la page d'édition d'un lieu avec une image principale et plusieurs images
2. Supprimer une image de lieu
3. Vérifier que l'image principale reste affichée
4. Vérifier que seule l'image supprimée disparaît de la liste des images de lieu

## Résultat attendu
- ✅ L'image principale reste visible après suppression d'une image de lieu
- ✅ Seule l'image sélectionnée est supprimée de la liste
- ✅ Aucun conflit de réactivité entre les deux types d'images
