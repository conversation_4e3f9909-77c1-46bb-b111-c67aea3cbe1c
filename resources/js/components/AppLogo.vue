<script setup lang="ts">
import AppLogoIcon from '@/components/AppLogoIcon.vue';

// Option pour utiliser une image ou l'icône SVG
const useImageLogo = false; // Changez à true pour utiliser une image
</script>

<template>
    <!-- Version avec image personnalisée -->
    <div v-if="useImageLogo" class="flex aspect-square size-8 items-center justify-center rounded-md overflow-hidden">
        <img
            src="/images/logo.png"
            alt="MbokaTour Logo"
            class="size-8 object-contain"
        />
    </div>

    <!-- Version avec icône SVG -->
    <div v-else class="flex aspect-square size-8 items-center justify-center rounded-md bg-yellow-500 text-white">
        <AppLogoIcon class="size-5 fill-current text-white" />
    </div>

    <div class="ml-1 grid flex-1 text-left text-sm">
        <span class="mb-0.5 truncate leading-tight font-semibold">MbokaTour</span>
        <span class="truncate text-xs text-muted-foreground">Découvrez Kinshasa</span>
    </div>
</template>
