<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { computed } from 'vue';

interface Props {
    title: string;
    value: number | string;
    icon: string;
    trend?: {
        value: number;
        isPositive: boolean;
        label: string;
    };
    color?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
    loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    color: 'primary',
    loading: false,
});

const colorClasses = computed(() => {
    const colors = {
        primary: 'text-primary-600 bg-primary-50 border-primary-200',
        success: 'text-green-600 bg-green-50 border-green-200',
        warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
        danger: 'text-red-600 bg-red-50 border-red-200',
        info: 'text-blue-600 bg-blue-50 border-blue-200',
    };
    return colors[props.color];
});

const trendClasses = computed(() => {
    if (!props.trend) return '';
    return props.trend.isPositive 
        ? 'text-green-600 bg-green-50' 
        : 'text-red-600 bg-red-50';
});

const formatValue = computed(() => {
    if (typeof props.value === 'number') {
        return props.value.toLocaleString();
    }
    return props.value;
});
</script>

<template>
    <Card class="relative overflow-hidden transition-all duration-200">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle class="text-sm font-medium text-muted-foreground">
                {{ title }}
            </CardTitle>
            <div 
                :class="[
                    'flex h-8 w-8 items-center justify-center rounded-lg border text-sm font-semibold',
                    colorClasses
                ]"
            >
                {{ icon }}
            </div>
        </CardHeader>
        <CardContent>
            <div class="space-y-2">
                <div class="text-2xl font-bold">
                    <span v-if="loading" class="animate-pulse">...</span>
                    <span v-else>{{ formatValue }}</span>
                </div>
                <div v-if="trend" class="flex items-center space-x-1">
                    <span 
                        :class="[
                            'inline-flex items-center rounded-full px-2 py-1 text-xs font-medium',
                            trendClasses
                        ]"
                    >
                        <span v-if="trend.isPositive">↗</span>
                        <span v-else>↘</span>
                        {{ Math.abs(trend.value) }}%
                    </span>
                    <span class="text-xs text-muted-foreground">{{ trend.label }}</span>
                </div>
            </div>
        </CardContent>
        
        <!-- Effet de brillance au survol -->
        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full transition-transform duration-1000 group-hover:translate-x-full"></div>
    </Card>
</template>
