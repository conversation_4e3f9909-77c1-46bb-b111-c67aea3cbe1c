<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface Comment {
    id: number;
    content: string;
    rating: number;
    user_name: string;
    place_name: string;
    created_at: string;
}

interface Props {
    comments: Comment[];
}

defineProps<Props>();

const getInitials = (name: string) => {
    return name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
};

const getStars = (rating: number) => {
    return '⭐'.repeat(rating) + '☆'.repeat(5 - rating);
};
</script>

<template>
    <Card>
        <CardHeader>
            <CardTitle class="flex items-center gap-2">
                <span class="text-lg">💬</span>
                Commentaires Récents
            </CardTitle>
        </CardHeader>
        <CardContent>
            <div class="space-y-4">
                <div 
                    v-for="comment in comments" 
                    :key="comment.id"
                    class="flex gap-3 p-3 rounded-lg border border-border/50 hover:border-border transition-colors"
                >
                    <!-- Avatar -->
                    <Avatar class="h-10 w-10 flex-shrink-0">
                        <AvatarFallback class="bg-primary-100 text-primary-700 text-sm font-medium">
                            {{ getInitials(comment.user_name) }}
                        </AvatarFallback>
                    </Avatar>

                    <!-- Contenu du commentaire -->
                    <div class="flex-1 min-w-0">
                        <div class="flex items-start justify-between gap-2">
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-1">
                                    <span class="font-medium text-sm">{{ comment.user_name }}</span>
                                    <span class="text-xs text-muted-foreground">•</span>
                                    <span class="text-xs text-muted-foreground">{{ comment.created_at }}</span>
                                </div>
                                <div class="text-xs text-muted-foreground mb-2">
                                    sur <span class="font-medium">{{ comment.place_name }}</span>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <div class="text-xs" :title="`${comment.rating}/5 étoiles`">
                                    {{ getStars(comment.rating) }}
                                </div>
                            </div>
                        </div>
                        
                        <p class="text-sm text-foreground leading-relaxed">
                            {{ comment.content }}
                        </p>
                    </div>
                </div>

                <div v-if="comments.length === 0" class="text-center py-8 text-muted-foreground">
                    <span class="text-2xl mb-2 block">💬</span>
                    <p>Aucun commentaire récent</p>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
