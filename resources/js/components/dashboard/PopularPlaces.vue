<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Place {
    id: number;
    name: string;
    views_count: number;
    likes_count: number;
    comments_count: number;
    categories: string[];
    main_image_url?: string;
}

interface Props {
    places: Place[];
    title?: string;
    showImage?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
    title: 'Lieux Populaires',
    showImage: true,
});

const getImageUrl = (url?: string) => {
    if (!url) return '/images/placeholder.jpg';
    return url.startsWith('http') ? url : `/storage/${url}`;
};
</script>

<template>
    <Card>
        <CardHeader>
            <CardTitle class="flex items-center gap-2">
                <span class="text-lg">🏆</span>
                {{ title }}
            </CardTitle>
        </CardHeader>
        <CardContent>
            <div class="space-y-4">
                <div 
                    v-for="(place, index) in places" 
                    :key="place.id"
                    class="flex items-center gap-3 p-3 rounded-lg border border-border/50 hover:border-border transition-colors"
                >
                    <!-- Rang -->
                    <div class="flex-shrink-0">
                        <div 
                            :class="[
                                'flex h-8 w-8 items-center justify-center rounded-full text-sm font-bold',
                                index === 0 ? 'bg-yellow-100 text-yellow-800' :
                                index === 1 ? 'bg-gray-100 text-gray-800' :
                                index === 2 ? 'bg-orange-100 text-orange-800' :
                                'bg-muted text-muted-foreground'
                            ]"
                        >
                            {{ index + 1 }}
                        </div>
                    </div>

                    <!-- Image (si activée) -->
                    <div v-if="showImage" class="flex-shrink-0">
                        <img 
                            :src="getImageUrl(place.main_image_url)" 
                            :alt="place.name"
                            class="h-12 w-12 rounded-lg object-cover"
                            @error="($event.target as HTMLImageElement).src = '/images/placeholder.jpg'"
                        />
                    </div>

                    <!-- Informations du lieu -->
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-sm truncate">{{ place.name }}</h4>
                        <div class="flex items-center gap-2 mt-1">
                            <div class="flex items-center gap-1 text-xs text-muted-foreground">
                                <span>👁</span>
                                <span>{{ place.views_count.toLocaleString() }}</span>
                            </div>
                            <div class="flex items-center gap-1 text-xs text-muted-foreground">
                                <span>❤️</span>
                                <span>{{ place.likes_count.toLocaleString() }}</span>
                            </div>
                            <div class="flex items-center gap-1 text-xs text-muted-foreground">
                                <span>💬</span>
                                <span>{{ place.comments_count.toLocaleString() }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Catégories -->
                    <div class="flex-shrink-0">
                        <div class="flex flex-wrap gap-1">
                            <Badge 
                                v-for="category in place.categories.slice(0, 2)" 
                                :key="category"
                                variant="secondary"
                                class="text-xs"
                            >
                                {{ category }}
                            </Badge>
                            <Badge 
                                v-if="place.categories.length > 2"
                                variant="outline"
                                class="text-xs"
                            >
                                +{{ place.categories.length - 2 }}
                            </Badge>
                        </div>
                    </div>
                </div>

                <div v-if="places.length === 0" class="text-center py-8 text-muted-foreground">
                    <span class="text-2xl mb-2 block">📍</span>
                    <p>Aucun lieu trouvé</p>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
