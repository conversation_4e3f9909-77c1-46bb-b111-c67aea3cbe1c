<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { computed, ref } from 'vue';

interface DayActivity {
    date: string;
    places: number;
    users: number;
    likes: number;
    comments: number;
}

interface Props {
    data: DayActivity[];
    metric?: 'places' | 'users' | 'likes' | 'comments';
}

const props = withDefaults(defineProps<Props>(), {
    metric: 'places',
});

const selectedMetric = ref(props.metric);

const metrics = [
    { key: 'places', label: 'Lieux', color: '#fcc804', icon: '📍' },
    { key: 'users', label: 'Utilisateurs', color: '#3b82f6', icon: '👥' },
    { key: 'likes', label: 'Likes', color: '#ef4444', icon: '❤️' },
    { key: 'comments', label: 'Commentaires', color: '#10b981', icon: '💬' },
];

const currentMetric = computed(() => {
    return metrics.find(m => m.key === selectedMetric.value) || metrics[0];
});

const chartData = computed(() => {
    const values = props.data.map(day => day[selectedMetric.value as keyof DayActivity] as number);
    const maxValue = Math.max(...values, 1);
    
    return props.data.map((day, index) => ({
        ...day,
        value: values[index],
        height: (values[index] / maxValue) * 100,
        date: new Date(day.date).toLocaleDateString('fr-FR', { 
            day: '2-digit', 
            month: '2-digit' 
        }),
    }));
});

const totalValue = computed(() => {
    return chartData.value.reduce((sum, day) => sum + day.value, 0);
});

const averageValue = computed(() => {
    return Math.round(totalValue.value / chartData.value.length);
});
</script>

<template>
    <Card>
        <CardHeader>
            <div class="flex items-center justify-between">
                <CardTitle class="flex items-center gap-2">
                    <span class="text-lg">📈</span>
                    Activité des 30 derniers jours
                </CardTitle>
                <div class="flex gap-1">
                    <button
                        v-for="metric in metrics"
                        :key="metric.key"
                        @click="selectedMetric = metric.key"
                        :class="[
                            'px-3 py-1 text-xs rounded-full border transition-colors',
                            selectedMetric === metric.key
                                ? 'bg-primary-100 text-primary-700 border-primary-300'
                                : 'bg-muted text-muted-foreground border-border hover:bg-muted/80'
                        ]"
                    >
                        {{ metric.icon }} {{ metric.label }}
                    </button>
                </div>
            </div>
        </CardHeader>
        <CardContent>
            <div class="space-y-4">
                <!-- Statistiques résumées -->
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-muted/50 rounded-lg">
                        <div class="text-2xl font-bold" :style="{ color: currentMetric.color }">
                            {{ totalValue.toLocaleString() }}
                        </div>
                        <div class="text-xs text-muted-foreground">Total</div>
                    </div>
                    <div class="text-center p-3 bg-muted/50 rounded-lg">
                        <div class="text-2xl font-bold" :style="{ color: currentMetric.color }">
                            {{ averageValue.toLocaleString() }}
                        </div>
                        <div class="text-xs text-muted-foreground">Moyenne/jour</div>
                    </div>
                </div>

                <!-- Graphique en barres -->
                <div class="relative">
                    <div class="flex items-end justify-between h-32 gap-1">
                        <div
                            v-for="(day, index) in chartData"
                            :key="day.date"
                            class="flex-1 flex flex-col items-center group"
                        >
                            <!-- Barre -->
                            <div class="relative w-full flex justify-center">
                                <div
                                    class="w-full max-w-6 rounded-t transition-all duration-300 hover:opacity-80"
                                    :style="{
                                        height: `${day.height}%`,
                                        backgroundColor: currentMetric.color,
                                        minHeight: day.value > 0 ? '4px' : '0px'
                                    }"
                                ></div>
                                
                                <!-- Tooltip au survol -->
                                <div class="absolute bottom-full mb-2 hidden group-hover:block">
                                    <div class="bg-gray-900 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
                                        {{ day.date }}: {{ day.value }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Axe des dates (affiché seulement pour certains points) -->
                    <div class="flex justify-between mt-2 text-xs text-muted-foreground">
                        <span v-for="(day, index) in chartData" :key="index" class="flex-1 text-center">
                            <span v-if="index % 5 === 0 || index === chartData.length - 1">
                                {{ day.date }}
                            </span>
                        </span>
                    </div>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
