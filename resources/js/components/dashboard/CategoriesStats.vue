<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from '@/components/ui/card';
import { computed } from 'vue';

interface CategoryStat {
    name: string;
    places_count: number;
    icon: string;
    color: string;
}

interface Props {
    categories: CategoryStat[];
}

const props = defineProps<Props>();

const maxCount = computed(() => {
    return Math.max(...props.categories.map(cat => cat.places_count), 1);
});

const getBarWidth = (count: number) => {
    return (count / maxCount.value) * 100;
};
</script>

<template>
    <Card>
        <CardHeader>
            <CardTitle class="flex items-center gap-2">
                <span class="text-lg">📊</span>
                Répartition par Catégories
            </CardTitle>
        </CardHeader>
        <CardContent>
            <div class="space-y-4">
                <div 
                    v-for="category in categories" 
                    :key="category.name"
                    class="space-y-2"
                >
                    <!-- En-tête de la catégorie -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-2">
                            <span class="text-lg">{{ category.icon }}</span>
                            <span class="font-medium text-sm">{{ category.name }}</span>
                        </div>
                        <span class="text-sm font-semibold text-muted-foreground">
                            {{ category.places_count }}
                        </span>
                    </div>

                    <!-- Barre de progression -->
                    <div class="relative">
                        <div class="h-2 bg-muted rounded-full overflow-hidden">
                            <div 
                                class="h-full bg-primary-500 rounded-full transition-all duration-500 ease-out"
                                :style="{ 
                                    width: `${getBarWidth(category.places_count)}%`,
                                    backgroundColor: category.color || '#fcc804'
                                }"
                            ></div>
                        </div>
                    </div>
                </div>

                <div v-if="categories.length === 0" class="text-center py-8 text-muted-foreground">
                    <span class="text-2xl mb-2 block">📂</span>
                    <p>Aucune catégorie trouvée</p>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
