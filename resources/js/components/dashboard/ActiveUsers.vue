<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface User {
    id: number;
    name: string;
    likes_count: number;
    favorites_count: number;
    comments_count: number;
}

interface Props {
    users: User[];
}

defineProps<Props>();

const getInitials = (name: string) => {
    return name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2);
};

const getTotalActivity = (user: User) => {
    return user.likes_count + user.favorites_count + user.comments_count;
};
</script>

<template>
    <Card>
        <CardHeader>
            <CardTitle class="flex items-center gap-2">
                <span class="text-lg">👑</span>
                Utilisateurs les Plus Actifs
            </CardTitle>
        </CardHeader>
        <CardContent>
            <div class="space-y-4">
                <div 
                    v-for="(user, index) in users" 
                    :key="user.id"
                    class="flex items-center gap-3 p-3 rounded-lg border border-border/50 hover:border-border transition-colors"
                >
                    <!-- Rang et Avatar -->
                    <div class="flex items-center gap-3">
                        <div 
                            :class="[
                                'flex h-6 w-6 items-center justify-center rounded-full text-xs font-bold',
                                index === 0 ? 'bg-yellow-100 text-yellow-800' :
                                index === 1 ? 'bg-gray-100 text-gray-800' :
                                index === 2 ? 'bg-orange-100 text-orange-800' :
                                'bg-muted text-muted-foreground'
                            ]"
                        >
                            {{ index + 1 }}
                        </div>
                        
                        <Avatar class="h-10 w-10">
                            <AvatarFallback class="bg-primary-100 text-primary-700 font-medium">
                                {{ getInitials(user.name) }}
                            </AvatarFallback>
                        </Avatar>
                    </div>

                    <!-- Informations utilisateur -->
                    <div class="flex-1 min-w-0">
                        <h4 class="font-medium text-sm truncate">{{ user.name }}</h4>
                        <div class="flex items-center gap-2 mt-1">
                            <Badge variant="secondary" class="text-xs">
                                {{ getTotalActivity(user) }} activités
                            </Badge>
                        </div>
                    </div>

                    <!-- Statistiques détaillées -->
                    <div class="flex-shrink-0">
                        <div class="grid grid-cols-3 gap-2 text-center">
                            <div class="text-xs">
                                <div class="font-semibold text-red-600">{{ user.likes_count }}</div>
                                <div class="text-muted-foreground">❤️</div>
                            </div>
                            <div class="text-xs">
                                <div class="font-semibold text-blue-600">{{ user.favorites_count }}</div>
                                <div class="text-muted-foreground">⭐</div>
                            </div>
                            <div class="text-xs">
                                <div class="font-semibold text-green-600">{{ user.comments_count }}</div>
                                <div class="text-muted-foreground">💬</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="users.length === 0" class="text-center py-8 text-muted-foreground">
                    <span class="text-2xl mb-2 block">👥</span>
                    <p>Aucun utilisateur actif</p>
                </div>
            </div>
        </CardContent>
    </Card>
</template>
