<script setup lang="ts">
import type { HTMLAttributes } from 'vue';

defineOptions({
    inheritAttrs: false,
});

interface Props {
    className?: HTMLAttributes['class'];
}

defineProps<Props>();
</script>

<template>
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" :class="className" v-bind="$attrs">
        <!-- Fond circulaire -->
        <circle cx="16" cy="16" r="15" fill="currentColor" opacity="0.1"/>

        <!-- Pin de localisation principal -->
        <path
            fill="currentColor"
            d="M16 4c-4.4 0-8 3.6-8 8 0 6 8 16 8 16s8-10 8-16c0-4.4-3.6-8-8-8zm0 11c-1.7 0-3-1.3-3-3s1.3-3 3-3 3 1.3 3 3-1.3 3-3 3z"
        />

        <!-- <PERSON><PERSON><PERSON> centrale (destination favorite) -->
        <path
            fill="currentColor"
            d="M16 9l1 3h3l-2.5 1.8 1 3L16 15l-2.5 1.8 1-3L12 12h3l1-3z"
            opacity="0.9"
        />

        <!-- Petites étoiles autour (autres destinations) -->
        <circle cx="8" cy="8" r="1" fill="currentColor" opacity="0.6"/>
        <circle cx="24" cy="8" r="1" fill="currentColor" opacity="0.6"/>
        <circle cx="6" cy="20" r="1" fill="currentColor" opacity="0.6"/>
        <circle cx="26" cy="20" r="1" fill="currentColor" opacity="0.6"/>

        <!-- Lignes de connexion subtiles -->
        <path
            stroke="currentColor"
            stroke-width="0.5"
            opacity="0.3"
            d="M8 8 L16 12 M24 8 L16 12 M6 20 L16 16 M26 20 L16 16"
            fill="none"
        />
    </svg>
</template>
