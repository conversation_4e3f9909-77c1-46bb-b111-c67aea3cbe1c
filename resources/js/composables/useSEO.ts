import { usePage } from '@inertiajs/vue3';
import { computed } from 'vue';

interface SEOConfig {
    title?: string;
    description?: string;
    keywords?: string[];
    image?: string;
    url?: string;
    type?: string;
    siteName?: string;
    locale?: string;
    twitterCard?: string;
    twitterSite?: string;
    twitterCreator?: string;
}

export function useSEO() {
    const page = usePage();

    const defaultConfig = {
        title: 'MbokaTour - Redécouvre Kinshasa autrement',
        description: 'L\'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi.',
        keywords: ['Kinshasa', 'tourisme', 'Congo', 'RDC', 'voyage', 'culture', 'événements', 'lieux', 'découverte', 'guide', 'application mobile', 'bons plans'],
        image: '/images/og-image.jpg',
        url: '/',
        type: 'website',
        siteName: 'MbokaTour',
        locale: 'fr_FR',
        twitterCard: 'summary_large_image',
        twitterSite: '@MbokaTour',
        twitterCreator: '@MbokaTour',
    };

    const seoData = computed(() => {
        return page.props.seo || {};
    });

    const generateMetaTags = (config: SEOConfig = {}) => {
        const finalConfig = { ...defaultConfig, ...config };
        
        return {
            title: finalConfig.title,
            meta: [
                // Basic meta tags
                { name: 'description', content: finalConfig.description },
                { name: 'keywords', content: Array.isArray(finalConfig.keywords) ? finalConfig.keywords.join(', ') : finalConfig.keywords },
                { name: 'robots', content: 'index,follow' },
                { name: 'author', content: 'MbokaTour' },
                
                // Open Graph
                { property: 'og:type', content: finalConfig.type },
                { property: 'og:url', content: finalConfig.url },
                { property: 'og:title', content: finalConfig.title },
                { property: 'og:description', content: finalConfig.description },
                { property: 'og:image', content: finalConfig.image },
                { property: 'og:site_name', content: finalConfig.siteName },
                { property: 'og:locale', content: finalConfig.locale },
                
                // Twitter
                { property: 'twitter:card', content: finalConfig.twitterCard },
                { property: 'twitter:url', content: finalConfig.url },
                { property: 'twitter:title', content: finalConfig.title },
                { property: 'twitter:description', content: finalConfig.description },
                { property: 'twitter:image', content: finalConfig.image },
                { property: 'twitter:site', content: finalConfig.twitterSite },
                { property: 'twitter:creator', content: finalConfig.twitterCreator },
                
                // Additional meta tags
                { name: 'theme-color', content: '#fcc804' },
                { name: 'msapplication-TileColor', content: '#fcc804' },
                { name: 'apple-mobile-web-app-capable', content: 'yes' },
                { name: 'apple-mobile-web-app-status-bar-style', content: 'default' },
                { name: 'apple-mobile-web-app-title', content: 'MbokaTour' },
            ],
            link: [
                { rel: 'canonical', href: finalConfig.url },
            ]
        };
    };

    const generateJsonLd = (config: SEOConfig = {}) => {
        const finalConfig = { ...defaultConfig, ...config };
        
        return {
            '@context': 'https://schema.org',
            '@type': finalConfig.type === 'website' ? 'WebApplication' : 'WebPage',
            name: 'MbokaTour',
            description: finalConfig.description,
            url: finalConfig.url,
            applicationCategory: 'TravelApplication',
            operatingSystem: 'iOS, Android, Web',
            offers: {
                '@type': 'Offer',
                price: '0',
                priceCurrency: 'USD'
            },
            author: {
                '@type': 'Organization',
                name: 'MbokaTour'
            },
            publisher: {
                '@type': 'Organization',
                name: 'MbokaTour'
            },
            inLanguage: 'fr-FR',
            potentialAction: {
                '@type': 'SearchAction',
                target: '/search?q={search_term_string}',
                'query-input': 'required name=search_term_string'
            }
        };
    };

    const generatePlaceJsonLd = (place: any) => {
        return {
            '@context': 'https://schema.org',
            '@type': 'Place',
            name: place.name,
            description: place.description,
            url: `/places/${place.slug || place.id}`,
            address: {
                '@type': 'PostalAddress',
                addressLocality: 'Kinshasa',
                addressCountry: 'CD'
            },
            geo: place.latitude && place.longitude ? {
                '@type': 'GeoCoordinates',
                latitude: place.latitude,
                longitude: place.longitude
            } : undefined,
            image: place.main_image_url || '/images/og-image.jpg',
            aggregateRating: place.average_rating ? {
                '@type': 'AggregateRating',
                ratingValue: place.average_rating,
                reviewCount: place.reviews_count || 0
            } : undefined
        };
    };

    return {
        seoData,
        generateMetaTags,
        generateJsonLd,
        generatePlaceJsonLd,
        defaultConfig
    };
}
