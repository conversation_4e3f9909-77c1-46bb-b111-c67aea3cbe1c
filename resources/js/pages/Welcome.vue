<script setup lang="ts">
import { Head } from '@inertiajs/vue3';
</script>

<template>
    <Head title="MbokaTour - Redécouvre Kinshasa autrement">
        <!-- Meta tags SEO -->
        <meta name="description" content="L'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi." />
        <meta name="keywords" content="Kinshasa, tourisme, Congo, RDC, voyage, culture, événements, lieux, découverte, guide, application mobile, bons plans" />
        <meta name="robots" content="index,follow" />
        <meta name="author" content="MbokaTour" />
        <link rel="canonical" href="/" />

        <!-- Open Graph / Facebook -->
        <meta property="og:type" content="website" />
        <meta property="og:url" content="/" />
        <meta property="og:title" content="MbokaTour - Redécouvre Kinshasa autrement" />
        <meta property="og:description" content="L'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi." />
        <meta property="og:image" content="/images/og-image.jpg" />
        <meta property="og:site_name" content="MbokaTour" />
        <meta property="og:locale" content="fr_FR" />

        <!-- Twitter -->
        <meta property="twitter:card" content="summary_large_image" />
        <meta property="twitter:url" content="/" />
        <meta property="twitter:title" content="MbokaTour - Redécouvre Kinshasa autrement" />
        <meta property="twitter:description" content="L'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi." />
        <meta property="twitter:image" content="/images/og-image.jpg" />
        <meta property="twitter:site" content="@MbokaTour" />
        <meta property="twitter:creator" content="@MbokaTour" />

        <!-- Additional meta tags -->
        <meta name="theme-color" content="#fcc804" />
        <meta name="msapplication-TileColor" content="#fcc804" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="MbokaTour" />

        <!-- Preconnect for performance -->
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />


    </Head>
    <div class="relative flex min-h-screen flex-col items-center p-6 text-[#1b1b18] lg:justify-center lg:p-8 overflow-hidden">
        <!-- Background Elements for Entire Page -->
        <div class="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-primary-100 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900"></div>

        <!-- Animated Background Shapes -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-200/30 rounded-full blur-3xl animate-pulse dark:bg-primary-800/20"></div>
            <div class="absolute -bottom-40 -left-40 w-96 h-96 bg-primary-300/20 rounded-full blur-3xl animate-pulse delay-1000 dark:bg-primary-700/10"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-primary-100/40 rounded-full blur-2xl animate-pulse delay-500 dark:bg-primary-900/20"></div>
            <div class="absolute top-1/4 right-1/4 w-48 h-48 bg-primary-200/25 rounded-full blur-2xl animate-pulse delay-700 dark:bg-primary-800/15"></div>
            <div class="absolute bottom-1/4 left-1/4 w-56 h-56 bg-primary-300/30 rounded-full blur-3xl animate-pulse delay-300 dark:bg-primary-700/15"></div>
        </div>

        <!-- Subtle Pattern Overlay -->
        <div class="absolute inset-0 opacity-30">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-primary-100/20 to-transparent dark:via-primary-800/10"></div>
        </div>
        <!-- Hero Section -->
        <!-- <header class="relative z-20 mb-6 w-full max-w-[335px] text-sm not-has-[nav]:hidden lg:max-w-4xl">
            <nav class="flex items-center justify-end gap-4">
                <Link
                    v-if="$page.props.auth.user"
                    :href="route('dashboard')"
                    class="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                >
                    Dashboard
                </Link>
                <template v-else>
                    <Link
                        :href="route('login')"
                        class="inline-block rounded-sm border border-transparent px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#19140035] dark:text-[#EDEDEC] dark:hover:border-[#3E3E3A]"
                    >
                        Log in
                    </Link>
                    <Link
                        :href="route('register')"
                        class="inline-block rounded-sm border border-[#19140035] px-5 py-1.5 text-sm leading-normal text-[#1b1b18] hover:border-[#1915014a] dark:border-[#3E3E3A] dark:text-[#EDEDEC] dark:hover:border-[#62605b]"
                    >
                        Register
                    </Link>
                </template>
            </nav>
        </header> -->
        <!-- Hero Section -->
        <div class="relative flex w-full items-center justify-center min-h-[80vh] lg:grow">
            <main class="relative z-10 flex w-full max-w-6xl flex-col items-center text-center px-4 sm:px-6 lg:px-8">
                <!-- Hero Content -->
                <div class="space-y-8 sm:space-y-12 lg:space-y-16">
                    <!-- Main Heading -->
                    <div class="space-y-6 sm:space-y-8">
                        <div class="space-y-4 sm:space-y-6">
                            <h1 class="text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-6xl lg:text-7xl dark:text-white">
                                <span class="block bg-gradient-to-r from-primary-400 via-primary-500 to-primary-600 bg-clip-text text-transparent drop-shadow-sm">
                                    MbokaTour
                                </span>
                            </h1>
                            <div class="mx-auto max-w-3xl">
                                <p class="text-lg text-gray-600 sm:text-xl md:text-2xl lg:text-3xl font-light leading-tight dark:text-gray-300">
                                    L'application qui te fait redécouvrir
                                    <span class="font-semibold text-gray-900 dark:text-white">Kinshasa</span>
                                    autrement
                                </p>
                            </div>
                        </div>

                        <div class="mx-auto max-w-4xl">
                            <p class="text-base text-gray-600 leading-relaxed sm:text-lg md:text-xl dark:text-gray-400">
                                Que tu sois touriste ou simplement Kinois en quête d'une bonne idée de sortie,
                                MbokaTour te propose les meilleurs endroits à visiter, les événements à ne pas rater,
                                et les bons plans près de chez toi.
                            </p>
                        </div>
                    </div>

                    <!-- Call to Action -->
                    <div class="space-y-6 sm:space-y-8">
                        <!-- Primary Actions -->
                        <!-- <div class="flex flex-col gap-4 sm:flex-row sm:gap-6 justify-center items-center">
                            <Link
                                v-if="!$page.props.auth.user"
                                :href="route('register')"
                                class="group relative inline-flex items-center justify-center w-full sm:w-auto px-8 py-4 text-base sm:text-lg font-medium text-gray-900 bg-gradient-to-r from-primary-400 to-primary-500 rounded-full hover:from-primary-500 hover:to-primary-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                Commencer l'aventure
                                <svg class="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                </svg>
                            </Link>
                            <Link
                                v-else
                                :href="route('dashboard')"
                                class="group relative inline-flex items-center justify-center w-full sm:w-auto px-8 py-4 text-base sm:text-lg font-medium text-gray-900 bg-gradient-to-r from-primary-400 to-primary-500 rounded-full hover:from-primary-500 hover:to-primary-600 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                            >
                                Accéder au Dashboard
                                <svg class="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                </svg>
                            </Link>
                            <button class="inline-flex items-center justify-center w-full sm:w-auto px-8 py-4 text-base sm:text-lg font-medium text-gray-700 bg-white/80 backdrop-blur-sm border-2 border-gray-200/50 rounded-full hover:border-primary-300 hover:bg-white transition-all duration-200 dark:bg-gray-800/80 dark:text-gray-300 dark:border-gray-600/50 dark:hover:border-primary-600 dark:hover:bg-gray-700">
                                Explorer Kinshasa
                            </button>
                        </div> -->

                        <!-- Download App Section -->
                        <div class="flex flex-col items-center space-y-4 sm:space-y-6">
                            <p class="text-sm sm:text-base text-gray-500 dark:text-gray-400 font-medium">
                                Télécharge l'application mobile
                            </p>
                            <div class="flex flex-col w-full max-w-sm sm:max-w-none sm:flex-row gap-3 sm:gap-4 justify-center items-center">
                                <!-- App Store Button -->
                                <a href="#" class="group inline-flex items-center justify-center w-full sm:w-auto px-4 sm:px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                    <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                                    </svg>
                                    <div class="text-left">
                                        <div class="text-xs">Télécharger sur</div>
                                        <div class="text-sm font-semibold">App Store</div>
                                    </div>
                                </a>

                                <!-- Google Play Button -->
                                <a href="#" class="group inline-flex items-center justify-center w-full sm:w-auto px-4 sm:px-6 py-3 bg-black text-white rounded-xl hover:bg-gray-800 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                                    <svg class="w-5 h-5 sm:w-6 sm:h-6 mr-3" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                                    </svg>
                                    <div class="text-left">
                                        <div class="text-xs">Disponible sur</div>
                                        <div class="text-sm font-semibold">Google Play</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Features Pills -->
                    <div class="flex flex-wrap justify-center gap-2 sm:gap-3 pt-6 sm:pt-8">
                        <span class="inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium bg-primary-100/80 backdrop-blur-sm text-primary-800 border border-primary-200/50 dark:bg-primary-900/50 dark:text-primary-200 dark:border-primary-800/50">
                            🏛️ Lieux authentiques
                        </span>
                        <span class="inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium bg-primary-200/80 backdrop-blur-sm text-primary-900 border border-primary-300/50 dark:bg-primary-800/50 dark:text-primary-100 dark:border-primary-700/50">
                            🎉 Événements culturels
                        </span>
                        <span class="inline-flex items-center px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-medium bg-primary-300/80 backdrop-blur-sm text-primary-950 border border-primary-400/50 dark:bg-primary-700/50 dark:text-primary-50 dark:border-primary-600/50">
                            💡 Bons plans
                        </span>
                    </div>
                </div>
            </main>
        </div>
        <div class="relative z-10 hidden h-14.5 lg:block"></div>
    </div>
</template>



