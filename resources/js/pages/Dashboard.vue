<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/vue3';
import StatCard from '@/components/dashboard/StatCard.vue';
import PopularPlaces from '@/components/dashboard/PopularPlaces.vue';
import RecentComments from '@/components/dashboard/RecentComments.vue';
import CategoriesStats from '@/components/dashboard/CategoriesStats.vue';
import ActivityChart from '@/components/dashboard/ActivityChart.vue';
import ActiveUsers from '@/components/dashboard/ActiveUsers.vue';

interface Props {
    stats: {
        total: {
            places: number;
            users: number;
            categories: number;
            likes: number;
            comments: number;
            favorites: number;
        };
        places: {
            active: number;
            featured: number;
            with_images: number;
        };
        recent: {
            places: number;
            users: number;
            likes: number;
            comments: number;
        };
    };
    popularPlaces: Array<{
        id: number;
        name: string;
        views_count: number;
        likes_count: number;
        comments_count: number;
        categories: string[];
        main_image_url?: string;
    }>;
    mostLikedPlaces: Array<{
        id: number;
        name: string;
        likes_count: number;
        views_count: number;
        comments_count: number;
        categories: string[];
        main_image_url?: string;
    }>;
    categoriesStats: Array<{
        name: string;
        places_count: number;
        icon: string;
        color: string;
    }>;
    dailyActivity: Array<{
        date: string;
        places: number;
        users: number;
        likes: number;
        comments: number;
    }>;
    activeUsers: Array<{
        id: number;
        name: string;
        likes_count: number;
        favorites_count: number;
        comments_count: number;
    }>;
    recentComments: Array<{
        id: number;
        content: string;
        rating: number;
        user_name: string;
        place_name: string;
        created_at: string;
    }>;
}

defineProps<Props>();

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

// Calculer les tendances pour les cartes de statistiques
const getRecentTrend = (recent: number, total: number) => {
    if (total === 0) return { value: 0, isPositive: true, label: 'cette semaine' };
    const percentage = Math.round((recent / total) * 100);
    return {
        value: percentage,
        isPositive: percentage > 0,
        label: 'cette semaine'
    };
};
</script>

<template>
    <Head title="Dashboard" />

    <AppLayout :breadcrumbs="breadcrumbs">
        <div class="flex h-full flex-1 flex-col gap-6 p-6">
            <!-- En-tête du dashboard -->
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold tracking-tight">Dashboard</h1>
                    <p class="text-muted-foreground">
                        Vue d'ensemble de votre plateforme MbokaTour
                    </p>
                </div>
                <div class="text-right">
                    <div class="text-sm text-muted-foreground">Dernière mise à jour</div>
                    <div class="text-sm font-medium">{{ new Date().toLocaleString('fr-FR') }}</div>
                </div>
            </div>

            <!-- Cartes de statistiques principales -->
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <StatCard
                    title="Total des Lieux"
                    :value="stats.total.places"
                    icon="📍"
                    color="primary"
                    :trend="getRecentTrend(stats.recent.places, stats.total.places)"
                />
                <StatCard
                    title="Utilisateurs"
                    :value="stats.total.users"
                    icon="👥"
                    color="info"
                    :trend="getRecentTrend(stats.recent.users, stats.total.users)"
                />
                <StatCard
                    title="Total des Likes"
                    :value="stats.total.likes"
                    icon="❤️"
                    color="danger"
                    :trend="getRecentTrend(stats.recent.likes, stats.total.likes)"
                />
                <StatCard
                    title="Commentaires"
                    :value="stats.total.comments"
                    icon="💬"
                    color="success"
                    :trend="getRecentTrend(stats.recent.comments, stats.total.comments)"
                />
            </div>

            <!-- Statistiques secondaires -->
            <div class="grid gap-4 md:grid-cols-3">
                <StatCard
                    title="Lieux Actifs"
                    :value="stats.places.active"
                    icon="✅"
                    color="success"
                />
                <StatCard
                    title="Lieux Mis en Avant"
                    :value="stats.places.featured"
                    icon="⭐"
                    color="warning"
                />
                <StatCard
                    title="Favoris"
                    :value="stats.total.favorites"
                    icon="🔖"
                    color="info"
                />
            </div>

            <!-- Graphique d'activité -->
            <div class="grid gap-6 lg:grid-cols-3">
                <div class="lg:col-span-2">
                    <ActivityChart :data="dailyActivity" />
                </div>
                <div>
                    <CategoriesStats :categories="categoriesStats" />
                </div>
            </div>

            <!-- Section des lieux populaires et utilisateurs actifs -->
            <div class="grid gap-6 lg:grid-cols-2">
                <PopularPlaces :places="popularPlaces" />
                <PopularPlaces
                    :places="mostLikedPlaces"
                    title="Lieux les Plus Likés"
                    :show-image="false"
                />
            </div>

            <!-- Section des commentaires récents et utilisateurs actifs -->
            <div class="grid gap-6 lg:grid-cols-2">
                <RecentComments :comments="recentComments" />
                <ActiveUsers :users="activeUsers" />
            </div>
        </div>
    </AppLayout>
</template>


