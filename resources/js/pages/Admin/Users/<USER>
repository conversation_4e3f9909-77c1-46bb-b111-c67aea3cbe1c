<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';

interface User {
  id: number;
  name: string;
  email: string | null;
  phone_number: string;
  roles: Array<{
    id: number;
    name: string;
  }>;
}

interface Role {
  id: number;
  name: string;
}

const props = defineProps<{
  user: User;
  roles: Role[];
}>();

const form = useForm({
  name: props.user.name,
  email: props.user.email || '',
  phone_number: props.user.phone_number,
  password: '',
  password_confirmation: '',
  role: props.user.roles.length > 0 ? props.user.roles[0].name : 'admin',
});

const submit = () => {
  form.put(route('admin.users.update', props.user.id), {
    onSuccess: () => {
      // Redirection automatique
    },
  });
};


</script>

<template>
  <Head :title="`Modifier - ${user.name}`" />
  <AppLayout>
    <div class="p-4 sm:p-6">
      <div class="sm:flex-auto">
        <h1 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
          Modifier l'utilisateur
        </h1>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
          Mettre à jour les informations de {{ user.name }}
        </p>
      </div>
      
      <div class="mt-8">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <!-- Nom -->
            <div class="sm:col-span-3">
              <label for="name" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Nom complet <span class="text-red-500">*</span>
              </label>
              <div class="mt-2">
                <input 
                  type="text" 
                  v-model="form.name" 
                  id="name" 
                  required
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="Nom complet de l'utilisateur"
                />
                <div v-if="form.errors.name" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.name }}
                </div>
              </div>
            </div>

            <!-- Email -->
            <div class="sm:col-span-3">
              <label for="email" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Email
              </label>
              <div class="mt-2">
                <input 
                  type="email" 
                  v-model="form.email" 
                  id="email" 
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="<EMAIL>"
                />
                <div v-if="form.errors.email" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.email }}
                </div>
              </div>
            </div>

            <!-- Téléphone -->
            <div class="sm:col-span-3">
              <label for="phone_number" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Numéro de téléphone <span class="text-red-500">*</span>
              </label>
              <div class="mt-2">
                <input 
                  type="tel" 
                  v-model="form.phone_number" 
                  id="phone_number" 
                  required
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="+243123456789"
                />
                <div v-if="form.errors.phone_number" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.phone_number }}
                </div>
              </div>
            </div>

            <!-- Rôle -->
            <div class="sm:col-span-3">
              <label for="role" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Rôle
              </label>
              <div class="mt-2">
                <select
                  v-model="form.role"
                  id="role"
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500"
                >
                  <option v-for="role in roles" :key="role.id" :value="role.name">
                    {{ role.name }}
                  </option>
                </select>
                <div v-if="form.errors.role" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.role }}
                </div>
              </div>
            </div>
          </div>

          <!-- Section mot de passe -->
          <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
            <h3 class="text-base font-semibold leading-6 text-gray-900 dark:text-white mb-4">
              Changer le mot de passe
            </h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 mb-6">
              Laissez vide pour conserver le mot de passe actuel.
            </p>
            
            <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
              <!-- Nouveau mot de passe -->
              <div class="sm:col-span-3">
                <label for="password" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Nouveau mot de passe
                </label>
                <div class="mt-2">
                  <input 
                    type="password" 
                    v-model="form.password" 
                    id="password" 
                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                    placeholder="Nouveau mot de passe"
                  />
                  <div v-if="form.errors.password" class="mt-2 text-sm text-red-600 dark:text-red-400">
                    {{ form.errors.password }}
                  </div>
                </div>
              </div>

              <!-- Confirmation mot de passe -->
              <div class="sm:col-span-3">
                <label for="password_confirmation" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Confirmer le nouveau mot de passe
                </label>
                <div class="mt-2">
                  <input 
                    type="password" 
                    v-model="form.password_confirmation" 
                    id="password_confirmation" 
                    class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                    placeholder="Confirmer le nouveau mot de passe"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Boutons d'action -->
          <div class="mt-6 flex items-center justify-end gap-x-6">
            <Link :href="route('admin.users.show', user.id)" class="text-sm font-semibold leading-6 text-gray-900 dark:text-white">
              Annuler
            </Link>
            <button 
              type="submit" 
              :disabled="form.processing"
              class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
            >
              <span v-if="form.processing">Mise à jour...</span>
              <span v-else>Mettre à jour</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
