<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link, router } from '@inertiajs/vue3';

interface User {
  id: number;
  name: string;
  email: string | null;
  phone_number: string;
  email_verified_at: string | null;
  created_at: string;
  updated_at: string;
  roles: Array<{
    id: number;
    name: string;
  }>;
}

const props = defineProps<{
  user: User;
}>();

const deleteUser = () => {
  if (confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur ${props.user.name} ?`)) {
    router.delete(route('admin.users.destroy', props.user.id));
  }
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getUserRole = () => {
  return props.user.roles.length > 0 ? props.user.roles[0].name : 'Aucun rôle';
};

const getStatusInfo = () => {
  if (props.user.email_verified_at) {
    return {
      text: 'Compte vérifié',
      class: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
      date: formatDate(props.user.email_verified_at)
    };
  }
  return {
    text: 'Compte non vérifié',
    class: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    date: null
  };
};
</script>

<template>
  <Head :title="`Utilisateur - ${user.name}`" />
  <AppLayout>
    <div class="p-4 sm:p-6">
      <!-- En-tête -->
      <div class="sm:flex sm:items-center sm:justify-between">
        <div class="sm:flex-auto">
          <h1 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">
            Détails de l'utilisateur
          </h1>
          <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">
            Informations complètes de {{ user.name }}
          </p>
        </div>
        <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
          <div class="flex space-x-3">
            <Link 
              :href="route('admin.users.edit', user.id)" 
              class="inline-flex items-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600"
            >
              Modifier
            </Link>
            <button 
              @click="deleteUser"
              class="inline-flex items-center rounded-md bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600"
            >
              Supprimer
            </button>
          </div>
        </div>
      </div>

      <!-- Contenu principal -->
      <div class="mt-8">
        <div class="overflow-hidden bg-white shadow dark:bg-gray-800 sm:rounded-lg">
          <div class="px-4 py-6 sm:px-6">
            <h3 class="text-base font-semibold leading-7 text-gray-900 dark:text-white">
              Informations personnelles
            </h3>
            <p class="mt-1 max-w-2xl text-sm leading-6 text-gray-500 dark:text-gray-400">
              Détails et statut du compte utilisateur.
            </p>
          </div>
          <div class="border-t border-gray-100 dark:border-gray-700">
            <dl class="divide-y divide-gray-100 dark:divide-gray-700">
              <!-- Nom -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Nom complet
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  {{ user.name }}
                </dd>
              </div>

              <!-- Email -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Adresse email
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  {{ user.email || 'Non renseigné' }}
                </dd>
              </div>

              <!-- Téléphone -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Numéro de téléphone
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  {{ user.phone_number }}
                </dd>
              </div>

              <!-- Rôle -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Rôle
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  <span class="inline-flex items-center rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700 ring-1 ring-inset ring-blue-700/10 dark:bg-blue-400/10 dark:text-blue-400 dark:ring-blue-400/30">
                    {{ getUserRole() }}
                  </span>
                </dd>
              </div>

              <!-- Statut -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Statut du compte
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  <div class="flex flex-col space-y-1">
                    <span :class="['inline-flex items-center rounded-md px-2 py-1 text-xs font-medium ring-1 ring-inset w-fit', getStatusInfo().class]">
                      {{ getStatusInfo().text }}
                    </span>
                    <span v-if="getStatusInfo().date" class="text-xs text-gray-500 dark:text-gray-400">
                      Vérifié le {{ getStatusInfo().date }}
                    </span>
                  </div>
                </dd>
              </div>

              <!-- Date de création -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Compte créé le
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  {{ formatDate(user.created_at) }}
                </dd>
              </div>

              <!-- Dernière modification -->
              <div class="px-4 py-6 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                <dt class="text-sm font-medium leading-6 text-gray-900 dark:text-white">
                  Dernière modification
                </dt>
                <dd class="mt-1 text-sm leading-6 text-gray-700 dark:text-gray-300 sm:col-span-2 sm:mt-0">
                  {{ formatDate(user.updated_at) }}
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Bouton de retour -->
        <div class="mt-6">
          <Link 
            :href="route('admin.users.index')" 
            class="inline-flex items-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 dark:bg-white/10 dark:text-white dark:ring-white/20 dark:hover:bg-white/20"
          >
            ← Retour à la liste
          </Link>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
