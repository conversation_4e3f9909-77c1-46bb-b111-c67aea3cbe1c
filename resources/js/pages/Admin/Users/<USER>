<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm, Link } from '@inertiajs/vue3';

interface Role {
  id: number;
  name: string;
}

const props = defineProps<{
  roles: Role[];
}>();

const form = useForm({
  name: '',
  email: '',
  phone_number: '',
  password: '',
  password_confirmation: '',
  role: props.roles.length > 0 ? props.roles[0].name : 'admin',
});

const submit = () => {
  form.post(route('admin.users.store'), {
    onSuccess: () => {
      // Redirection automatique vers l'index
    },
  });
};
</script>

<template>
  <Head title="Create User" />
  <AppLayout>
    <div class="p-4 sm:p-6">
      <div class="sm:flex-auto">
        <h1 class="text-base font-semibold leading-6 text-gray-900 dark:text-white"><PERSON><PERSON>er un utilisateur</h1>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Ajouter un nouvel utilisateur au système.</p>
      </div>
      
      <div class="mt-8">
        <form @submit.prevent="submit" class="space-y-6">
          <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <!-- Nom -->
            <div class="sm:col-span-3">
              <label for="name" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Nom complet <span class="text-red-500">*</span>
              </label>
              <div class="mt-2">
                <input 
                  type="text" 
                  v-model="form.name" 
                  id="name" 
                  required
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="Nom complet de l'utilisateur"
                />
                <div v-if="form.errors.name" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.name }}
                </div>
              </div>
            </div>

            <!-- Email -->
            <div class="sm:col-span-3">
              <label for="email" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Email
              </label>
              <div class="mt-2">
                <input 
                  type="email" 
                  v-model="form.email" 
                  id="email" 
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="<EMAIL>"
                />
                <div v-if="form.errors.email" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.email }}
                </div>
              </div>
            </div>

            <!-- Téléphone -->
            <div class="sm:col-span-3">
              <label for="phone_number" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Numéro de téléphone <span class="text-red-500">*</span>
              </label>
              <div class="mt-2">
                <input 
                  type="tel" 
                  v-model="form.phone_number" 
                  id="phone_number" 
                  required
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="+243123456789"
                />
                <div v-if="form.errors.phone_number" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.phone_number }}
                </div>
              </div>
            </div>

            <!-- Rôle -->
            <div class="sm:col-span-3">
              <label for="role" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Rôle
              </label>
              <div class="mt-2">
                <select
                  v-model="form.role"
                  id="role"
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500"
                >
                  <option v-for="role in roles" :key="role.id" :value="role.name">
                    {{ role.name }}
                  </option>
                </select>
                <div v-if="form.errors.role" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.role }}
                </div>
              </div>
            </div>

            <!-- Mot de passe -->
            <div class="sm:col-span-3">
              <label for="password" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Mot de passe <span class="text-red-500">*</span>
              </label>
              <div class="mt-2">
                <input 
                  type="password" 
                  v-model="form.password" 
                  id="password" 
                  required
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="Mot de passe sécurisé"
                />
                <div v-if="form.errors.password" class="mt-2 text-sm text-red-600 dark:text-red-400">
                  {{ form.errors.password }}
                </div>
              </div>
            </div>

            <!-- Confirmation mot de passe -->
            <div class="sm:col-span-3">
              <label for="password_confirmation" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">
                Confirmer le mot de passe <span class="text-red-500">*</span>
              </label>
              <div class="mt-2">
                <input 
                  type="password" 
                  v-model="form.password_confirmation" 
                  id="password_confirmation" 
                  required
                  class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" 
                  placeholder="Confirmer le mot de passe"
                />
              </div>
            </div>
          </div>

          <!-- Boutons d'action -->
          <div class="mt-6 flex items-center justify-end gap-x-6">
            <Link :href="route('admin.users.index')" class="text-sm font-semibold leading-6 text-gray-900 dark:text-white">
              Annuler
            </Link>
            <button 
              type="submit" 
              :disabled="form.processing"
              class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 disabled:opacity-50"
            >
              <span v-if="form.processing">Création...</span>
              <span v-else>Créer l'utilisateur</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>
