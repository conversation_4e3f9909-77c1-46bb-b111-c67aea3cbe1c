<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';

const props = defineProps<{
  category: {
    id: number;
    name: string;
    type: string;
    icon: string;
    color: string;
    display_order: number;
  };
}>();

const form = useForm({
  name: props.category.name,
  type: props.category.type,
  icon: props.category.icon,
  color: props.category.color,
  display_order: props.category.display_order,
});

const submit = () => {
  form.put(route('admin.categories.update', props.category.id));
};
</script>

<template>
  <Head title="Edit Category" />
  <AppLayout>
    <div class="p-4 sm:p-6">
      <div class="sm:flex-auto">
        <h1 class="text-base font-semibold leading-6 text-gray-900 dark:text-white">Edit Category</h1>
        <p class="mt-2 text-sm text-gray-700 dark:text-gray-300">Edit the category details.</p>
      </div>
      <div class="mt-8">
        <form @submit.prevent="submit">
          <div class="grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
            <div class="sm:col-span-3">
              <label for="name" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">Name</label>
              <div class="mt-2">
                <input type="text" v-model="form.name" id="name" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" />
              </div>
            </div>
            <div class="sm:col-span-3">
              <label for="type" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">Type</label>
              <div class="mt-2">
                <input type="text" v-model="form.type" id="type" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" />
              </div>
            </div>
            <div class="sm:col-span-3">
              <label for="icon" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">Icon</label>
              <div class="mt-2">
                <input type="text" v-model="form.icon" id="icon" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" />
              </div>
            </div>
            <div class="sm:col-span-3">
              <label for="color" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">Color</label>
              <div class="mt-2">
                <input type="text" v-model="form.color" id="color" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" />
              </div>
            </div>
            <div class="sm:col-span-3">
              <label for="display_order" class="block text-sm font-medium leading-6 text-gray-900 dark:text-white">Display Order</label>
              <div class="mt-2">
                <input type="number" v-model="form.display_order" id="display_order" class="block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6 dark:bg-white/5 dark:text-white dark:ring-white/10 dark:focus:ring-indigo-500" />
              </div>
            </div>
          </div>
          <div class="mt-6 flex items-center justify-end gap-x-6">
            <button type="button" class="text-sm font-semibold leading-6 text-gray-900 dark:text-white">Cancel</button>
            <button type="submit" class="rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600">Save</button>
          </div>
        </form>
      </div>
    </div>
  </AppLayout>
</template>