<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import InputError from '@/components/InputError.vue';

defineProps<{
    categories: any[];
}>();

const form = useForm({
    name: '',
    description: '',
    location: '',
    address: '',
    neighborhood: '',
    city: '',
    latitude: '',
    longitude: '',
    price: '',
    opening_hours: '',
    is_free: false,
    status: 'active',
    main_image_url: null as File | null,
    mediaType: '',
    images: [] as File[],
    categories: [] as number[],
});

function getMediaType(file: File): string {
    const extension = file.name.split('.').pop()?.toLowerCase();

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp'];
    const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'ogg', 'qt'];

    if (extension && imageExtensions.includes(extension)) {
        return 'image';
    } else if (extension && videoExtensions.includes(extension)) {
        return 'video';
    } else {
        // Default to image for unknown types
        return 'image';
    }
}

function handleMainImageUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files && target.files.length > 0) {
        const file = target.files[0];
        form.main_image_url = file;
        form.mediaType = getMediaType(file);
    }
}

function handleFileUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files) {
        form.images = Array.from(target.files);
    }
}


function submit() {
    form
        .transform(data => ({
            ...data,
            is_free: data.is_free ? 1 : 0,
        }))
        .post(route('admin.places.store'));
}
</script>

<template>
    <Head title="Create Place" />
    <AppLayout>
        <div class="container mx-auto p-4">
            <h1 class="text-2xl font-bold mb-4">Create Place</h1>
            <form @submit.prevent="submit" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="name">Name</Label>
                        <Input id="name" type="text" v-model="form.name" />
                        <InputError :message="form.errors.name" />
                    </div>
                    <div>
                        <Label for="city">City</Label>
                        <Input id="city" type="text" v-model="form.city" />
                        <InputError :message="form.errors.city" />
                    </div>
                </div>

                <div>
                    <Label for="description">Description</Label>
                    <Textarea id="description" v-model="form.description" />
                    <InputError :message="form.errors.description" />
                </div>

                <div>
                    <Label for="location">Location</Label>
                    <Input id="location" type="text" v-model="form.location" />
                    <InputError :message="form.errors.location" />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="address">Address</Label>
                        <Input id="address" type="text" v-model="form.address" />
                        <InputError :message="form.errors.address" />
                    </div>
                    <div>
                        <Label for="neighborhood">Neighborhood</Label>
                        <Input id="neighborhood" type="text" v-model="form.neighborhood" />
                        <InputError :message="form.errors.neighborhood" />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="latitude">Latitude</Label>
                        <Input id="latitude" type="text" v-model="form.latitude" />
                        <InputError :message="form.errors.latitude" />
                    </div>
                    <div>
                        <Label for="longitude">Longitude</Label>
                        <Input id="longitude" type="text" v-model="form.longitude" />
                        <InputError :message="form.errors.longitude" />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="price">Price</Label>
                        <Input id="price" type="text" v-model="form.price" />
                        <InputError :message="form.errors.price" />
                    </div>
                    <div class="flex items-center space-x-2 mt-6">
                        <Checkbox id="is_free" v-model:checked="form.is_free" />
                        <Label for="is_free">Free</Label>
                        <InputError :message="form.errors.is_free" />
                    </div>
                </div>

                <div>
                    <Label for="opening_hours">Opening Hours</Label>
                    <Textarea id="opening_hours" v-model="form.opening_hours" />
                    <InputError :message="form.errors.opening_hours" />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="status">Status</Label>
                        <Select v-model="form.status">
                            <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                            </SelectContent>
                        </Select>
                        <InputError :message="form.errors.status" />
                    </div>
                    <div>
                        <Label for="categories">Categories</Label>
                        <select
                            id="categories"
                            v-model="form.categories"
                            multiple
                            class="flex w-full h-32 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            <option v-for="category in categories" :key="category.id" :value="category.id" class="p-2">
                                {{ category.name }}
                            </option>
                        </select>
                        <InputError :message="form.errors.categories" />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="main_image_url">Main Image/Video</Label>
                        <Input id="main_image_url" type="file" @change="handleMainImageUpload" />
                        <InputError :message="form.errors.main_image_url" />
                    </div>
                    <div>
                        <Label for="mediaType">Media Type (Auto-detected: Image or Video)</Label>
                        <Input
                            id="mediaType"
                            type="text"
                            v-model="form.mediaType"
                            readonly
                            class="bg-gray-100 cursor-not-allowed"
                            placeholder="Upload a file to auto-detect type (image/video)"
                        />
                        <InputError :message="form.errors.mediaType" />
                    </div>
                </div>

                <div>
                    <Label for="images">Images</Label>
                    <Input id="images" type="file" @change="handleFileUpload" multiple />
                    <InputError :message="form.errors.images" />
                </div>

                <Button type="submit" :disabled="form.processing">Create Place</Button>
            </form>
        </div>
    </AppLayout>
</template>