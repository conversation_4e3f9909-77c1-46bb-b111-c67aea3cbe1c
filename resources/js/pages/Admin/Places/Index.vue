<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, Link } from '@inertiajs/vue3';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';

defineProps<{
    places: any[];
}>();
</script>

<template>
    <Head title="Places" />
    <AppLayout>
        <div class="container mx-auto p-4">
            <div class="flex justify-between items-center mb-4">
                <h1 class="text-2xl font-bold">Places</h1>
                <Link :href="route('admin.places.create')">
                    <Button>Create Place</Button>
                </Link>
            </div>
            <Table>
                <TableHeader>
                    <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>City</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Actions</TableHead>
                    </TableRow>
                </TableHeader>
                <TableBody>
                    <TableRow v-for="place in places" :key="place.id">
                        <TableCell>{{ place.name }}</TableCell>
                        <TableCell>{{ place.city }}</TableCell>
                        <TableCell>{{ place.status }}</TableCell>
                        <TableCell class="space-x-2">
                            <Link :href="route('admin.places.show', place.id)">
                                <Button variant="outline" size="sm">Show</Button>
                            </Link>
                            <Link :href="route('admin.places.edit', place.id)">
                                <Button variant="outline" size="sm">Edit</Button>
                            </Link>
                            <Link :href="route('admin.places.destroy', place.id)" method="delete" as="button">
                                <Button variant="destructive" size="sm">Delete</Button>
                            </Link>
                        </TableCell>
                    </TableRow>
                </TableBody>
            </Table>
        </div>
    </AppLayout>
</template>