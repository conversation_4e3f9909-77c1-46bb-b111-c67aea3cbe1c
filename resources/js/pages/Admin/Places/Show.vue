<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head } from '@inertiajs/vue3';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '@/components/ui/carousel';

defineProps<{
    place: any;
}>();

const getFileUrl = (path: string) => {
    return path ;
};
</script>

<template>
    <Head :title="`Show Place: ${place.name}`" />
    <AppLayout>
        <div class="container mx-auto p-4">
            <Card>
                <CardHeader>
                    <CardTitle class="text-2xl font-bold">{{ place.name }}</CardTitle>
                </CardHeader>
                <CardContent class="space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Left Column -->
                        <div class="md:col-span-2 space-y-6">
                            <div>
                                <h3 class="text-lg font-semibold mb-2">Main Image/Video</h3>
                                <div v-if="place.main_image_url">
                                    <video v-if="place.main_image_url.endsWith('.mp4') || place.main_image_url.endsWith('.mov') || place.main_image_url.endsWith('.ogg') || place.main_image_url.endsWith('.qt')" :src="getFileUrl(place.main_image_url)" controls class="w-full max-h-96 object-cover rounded-lg shadow-md"></video>
                                    <img v-else :src="getFileUrl(place.main_image_url)" :alt="place.name" class="w-full max-h-96 object-cover rounded-lg shadow-md">
                                </div>
                                <p v-else class="text-gray-500">No main image or video.</p>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold mb-2">Description</h3>
                                <p class="text-gray-700">{{ place.description }}</p>
                            </div>

                            <div>
                                <h3 class="text-lg font-semibold mb-2">Images</h3>
                                <Carousel v-if="place.images && place.images.length > 0" class="w-full">
                                    <CarouselContent>
                                        <CarouselItem v-for="image in place.images" :key="image.id" class="md:basis-1/2 lg:basis-1/3">
                                            <img :src="getFileUrl(image.image_url)" :alt="place.name" class="w-full h-48 object-cover rounded-lg">
                                        </CarouselItem>
                                    </CarouselContent>
                                    <CarouselPrevious />
                                    <CarouselNext />
                                </Carousel>
                                <p v-else class="text-gray-500">No additional images.</p>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-4">
                            <div class="p-4 border rounded-lg">
                                <h3 class="font-semibold mb-2">Details</h3>
                                <div class="space-y-2">
                                    <p><strong>Location:</strong> {{ place.location }}</p>
                                    <p><strong>Address:</strong> {{ place.address }}</p>
                                    <p><strong>Neighborhood:</strong> {{ place.neighborhood }}</p>
                                    <p><strong>City:</strong> {{ place.city }}</p>
                                    <p><strong>Coordinates:</strong> {{ place.latitude }}, {{ place.longitude }}</p>
                                </div>
                            </div>

                            <div class="p-4 border rounded-lg">
                                <h3 class="font-semibold mb-2">Info</h3>
                                <div class="space-y-2">
                                    <p><strong>Price:</strong> {{ place.price }}</p>
                                    <p><strong>Opening Hours:</strong> {{ place.opening_hours }}</p>
                                    <p><strong>Status:</strong> <span :class="place.status === 'active' ? 'text-green-600' : 'text-red-600'">{{ place.status }}</span></p>
                                </div>
                            </div>

                            <div class="p-4 border rounded-lg">
                                <h3 class="font-semibold mb-2">Categories</h3>
                                <ul class="flex flex-wrap gap-2">
                                    <li v-for="category in place.categories" :key="category.id" class="bg-gray-200 text-gray-800 text-xs font-semibold mr-2 px-2.5 py-0.5 rounded-full">{{ category.name }}</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </CardContent>
            </Card>
        </div>
    </AppLayout>
</template>