<script setup lang="ts">
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import InputError from '@/components/InputError.vue';
import { ref, nextTick, computed, onUnmounted } from 'vue';

// Declare File type for TypeScript
declare global {
    interface Window {
        File: typeof File;
    }
}

const props = defineProps<{
    place: any;
    categories: any[];
}>();

const maxSize = 100 * 1024 * 1024; // 100 Mo
const allowedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
const allowedVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/webm', 'video/ogg'];
const allowedTypes = [...allowedImageTypes, ...allowedVideoTypes];

// Créer des copies réactives locales pour éviter les problèmes de réactivité
const localImages = ref([...props.place.images]);

// Variables pour l'image principale (séparées et protégées)
const currentMainImageUrl = ref(props.place.main_image_url);
const currentMediaType = ref(props.place.mediaType);

// Flag pour indiquer si l'image principale doit être supprimée
const shouldDeleteMainImage = ref(false);

// États de chargement et d'erreur
const isUploadingMainImage = ref(false);
const isUploadingImages = ref(false);
const isDeletingImage = ref<number | null>(null);
// const uploadProgress = ref(0); // Unused for now

// URLs de prévisualisation pour éviter les fuites mémoire
const previewUrls = ref<Map<string, string>>(new Map());

// Computed properties pour les vérifications de type
const isMainImageFileSelected = computed(() => form.main_image_url && form.main_image_url instanceof File);
const mainImagePreviewUrl = computed(() => {
    if (form.main_image_url && form.main_image_url instanceof File) {
        return createPreviewUrl(form.main_image_url);
    }
    return '';
});



// Fonction helper pour créer des URLs d'aperçu sécurisées avec gestion mémoire
function createPreviewUrl(file: File): string {
    try {
        const fileKey = `${file.name}-${file.size}-${file.lastModified}`;

        // Vérifier si on a déjà une URL pour ce fichier
        if (previewUrls.value.has(fileKey)) {
            return previewUrls.value.get(fileKey)!;
        }

        const url = URL.createObjectURL(file);
        previewUrls.value.set(fileKey, url);
        return url;
    } catch (error) {
        console.error('Error creating object URL:', error);
        return '';
    }
}

// Fonction pour nettoyer les URLs de prévisualisation
function cleanupPreviewUrls() {
    previewUrls.value.forEach(url => {
        try {
            URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Error revoking object URL:', error);
        }
    });
    previewUrls.value.clear();
}

// Validation des fichiers
function validateFile(file: File): { isValid: boolean; error?: string } {
    if (file.size > maxSize) {
        return { isValid: false, error: `Le fichier "${file.name}" est trop volumineux (max 100MB)` };
    }

    if (!allowedTypes.includes(file.type)) {
        return { isValid: false, error: `Le type de fichier "${file.type}" n'est pas autorisé` };
    }

    return { isValid: true };
}
const form = useForm({
    name: props.place.name,
    description: props.place.description,
    location: props.place.location,
    address: props.place.address,
    neighborhood: props.place.neighborhood,
    city: props.place.city,
    latitude: props.place.latitude,
    longitude: props.place.longitude,
    price: props.place.price,
    opening_hours: props.place.opening_hours,
    is_free: props.place.is_free,
    status: props.place.status,
    main_image_url: null as File | null,
    mediaType: props.place.mediaType || '',
    images: [] as File[],
    categories: props.place.categories.map((c: any) => c.id),
});

function getMediaType(file: File): string {
    const extension = file.name.split('.').pop()?.toLowerCase();

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp'];
    const videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'ogg', 'qt'];

    if (extension && imageExtensions.includes(extension)) {
        return 'image';
    } else if (extension && videoExtensions.includes(extension)) {
        return 'video';
    } else {
        // Default to image for unknown types
        return 'image';
    }
}

function handleMainImageUpload(event: Event) {
    const target = event.target as HTMLInputElement;

    if (target.files && target.files.length > 0) {
        const file = target.files[0];

        // Validation du fichier
        const validation = validateFile(file);
        if (!validation.isValid) {
            form.setError('main_image_url', validation.error!);
            target.value = '';
            form.main_image_url = null;
            form.mediaType = '';
            return;
        }

        // Nettoyer l'ancienne prévisualisation si elle existe
        if (form.main_image_url instanceof File) {
            cleanupPreviewUrls();
        }

        form.main_image_url = file;
        form.mediaType = getMediaType(file);
        form.clearErrors('main_image_url');

        // Réinitialiser le flag de suppression car une nouvelle image est sélectionnée
        shouldDeleteMainImage.value = false;

        console.log(`Image principale sélectionnée: ${file.name} (${(file.size / 1024 / 1024).toFixed(2)}MB)`);
    } else {
        // Nettoyer lors de la désélection
        if (form.main_image_url instanceof File) {
            cleanupPreviewUrls();
        }
        form.main_image_url = null;
        form.mediaType = '';
    }
}

function handleFileUpload(event: Event) {
    const target = event.target as HTMLInputElement;
    if (target.files) {
        const files = Array.from(target.files);
        const validFiles: File[] = [];
        const errors: string[] = [];

        // Valider chaque fichier
        files.forEach(file => {
            const validation = validateFile(file);
            if (validation.isValid) {
                validFiles.push(file);
            } else {
                errors.push(validation.error!);
            }
        });

        if (errors.length > 0) {
            form.setError('images', errors.join(' | '));
            if (validFiles.length === 0) {
                target.value = '';
                form.images = [];
                return;
            }
        }

        // Nettoyer les anciennes prévisualisations
        if (form.images.length > 0) {
            cleanupPreviewUrls();
        }

        form.images = validFiles;
        form.clearErrors('images');

        console.log(`${validFiles.length} images sélectionnées pour upload`);
        if (errors.length > 0) {
            console.warn(`${errors.length} fichiers rejetés:`, errors);
        }
    } else {
        // Nettoyer lors de la désélection
        if (form.images.length > 0) {
            cleanupPreviewUrls();
        }
        form.images = [];
    }
}


function submit() {
    // Validation avant soumission
    if (form.processing) {
        console.warn('Formulaire déjà en cours de traitement');
        return;
    }

    console.log('Soumission du formulaire avec:', {
        mainImage: form.main_image_url ? 'Fichier sélectionné' : 'Aucun',
        mediaType: form.mediaType,
        newImages: form.images.length,
        existingImages: localImages.value.length
    });

    form
        .transform(data => {
            const transformedData: any = {
                ...data,
                is_free: data.is_free ? 1 : 0,
            };

            // IMPORTANT: Gérer les différents cas pour l'image principale
            if (!data.main_image_url) {
                if (shouldDeleteMainImage.value) {
                    // Cas 1: L'utilisateur veut supprimer l'image principale
                    transformedData.main_image_url = null;
                    transformedData.mediaType = '';

                    console.log('Données transformées envoyées (suppression image principale):', {
                        hasMainImage: false,
                        deleteMainImage: true,
                        imagesCount: transformedData.images?.length || 0
                    });
                } else {
                    // Cas 2: Aucune nouvelle image, préserver l'existante
                    const { main_image_url, mediaType, ...dataWithoutMainImage } = transformedData;
                    const finalData = dataWithoutMainImage;

                    console.log('Données transformées envoyées (préservation image existante):', {
                        hasMainImage: false,
                        preserveExisting: true,
                        imagesCount: finalData.images?.length || 0
                    });

                    return finalData;
                }
            }

            console.log('Données transformées envoyées (avec nouvelle image principale):', {
                hasMainImage: !!transformedData.main_image_url,
                mediaType: transformedData.mediaType,
                imagesCount: transformedData.images?.length || 0,
                mainImageDetails: transformedData.main_image_url instanceof File ? {
                    name: transformedData.main_image_url.name,
                    size: transformedData.main_image_url.size,
                    type: transformedData.main_image_url.type,
                    lastModified: transformedData.main_image_url.lastModified
                } : 'Not a file'
            });

            return transformedData;
        })
        .post(route('admin.places.update-with-images', props.place.id), {
            forceFormData: true,
            onSuccess: () => {
                console.log('Mise à jour réussie');
                cleanupPreviewUrls();
                // Réinitialiser le flag de suppression après succès
                shouldDeleteMainImage.value = false;
            },
            onError: (errors) => {
                console.error('Erreurs de validation complètes:', errors);
                console.error('Erreur main_image_url:', errors.main_image_url);
                console.error('Erreur mediaType:', errors.mediaType);

                // Afficher les détails du fichier si c'est une vidéo
                if (form.main_image_url instanceof File) {
                    console.error('Détails du fichier:', {
                        name: form.main_image_url.name,
                        size: form.main_image_url.size,
                        type: form.main_image_url.type,
                        mediaType: form.mediaType
                    });
                }
            },
            onFinish: () => {
                console.log('Traitement terminé');
            }
        });
}

// Nettoyage lors de la destruction du composant
onUnmounted(() => {
    cleanupPreviewUrls();
});

// Fonction pour supprimer l'image principale
async function deleteMainImage() {
    if (!confirm('Êtes-vous sûr de vouloir supprimer l\'image principale ?')) {
        return;
    }

    try {
        isUploadingMainImage.value = true;

        // Marquer l'image comme devant être supprimée
        shouldDeleteMainImage.value = true;

        // Réinitialiser l'image principale dans le formulaire
        form.main_image_url = null;
        form.mediaType = '';

        // Réinitialiser l'affichage local
        currentMainImageUrl.value = null;
        currentMediaType.value = '';

        // Nettoyer les prévisualisations
        cleanupPreviewUrls();

        console.log('Image principale marquée pour suppression');

        // Note: La suppression effective se fera lors de la soumission du formulaire

    } catch (error) {
        console.error('Erreur lors de la suppression de l\'image principale:', error);
        alert('Erreur lors de la suppression de l\'image principale');
    } finally {
        isUploadingMainImage.value = false;
    }
}

// Fonction améliorée pour supprimer une image de lieu
async function deleteImage(imageId: number) {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette image ?')) {
        return;
    }

    try {
        isDeletingImage.value = imageId;

        // Vérifier que l'image existe dans la liste locale
        const imageExists = localImages.value.find((img: any) => img.id === imageId);
        if (!imageExists) {
            console.error('Image non trouvée dans la liste locale:', imageId);
            alert('Image non trouvée');
            return;
        }

        console.log('Tentative de suppression de l\'image:', {
            imageId,
            placeId: props.place.id,
            imageUrl: imageExists.image_url
        });

        // Construire l'URL de la route
        const deleteUrl = route('admin.places.images.delete', {
            place: props.place.id,
            image: imageId
        });

        console.log('URL de suppression:', deleteUrl);

        // Récupérer le token CSRF
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
        if (!csrfToken) {
            console.error('Token CSRF non trouvé');
            alert('Erreur de sécurité: Token CSRF manquant');
            return;
        }

        console.log('Token CSRF trouvé:', csrfToken.substring(0, 10) + '...');

        const response = await fetch(deleteUrl, {
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
            },
        });

        console.log('Réponse du serveur:', {
            status: response.status,
            statusText: response.statusText,
            ok: response.ok
        });

        if (response.ok) {
            // Supprimer uniquement l'image de la liste des images de lieu
            const imageIndex = localImages.value.findIndex((img: any) => img.id === imageId);
            if (imageIndex > -1) {
                const deletedImage = localImages.value[imageIndex];
                const newImages = [...localImages.value];
                newImages.splice(imageIndex, 1);
                localImages.value = newImages;

                console.log(`Image ${imageId} (${deletedImage.image_url}) supprimée avec succès. Images restantes: ${localImages.value.length}`);

                // Notification de succès
                alert('Image supprimée avec succès');
            }
        } else {
            // Essayer de lire la réponse d'erreur
            let errorData;
            const contentType = response.headers.get('content-type');

            if (contentType && contentType.includes('application/json')) {
                try {
                    errorData = await response.json();
                } catch (e) {
                    console.error('Erreur lors du parsing JSON:', e);
                    errorData = { error: `Erreur HTTP ${response.status}: ${response.statusText}` };
                }
            } else {
                // Si ce n'est pas du JSON, lire comme texte
                try {
                    const textResponse = await response.text();
                    console.error('Réponse non-JSON du serveur:', textResponse);
                    errorData = { error: `Erreur HTTP ${response.status}: ${textResponse || response.statusText}` };
                } catch (e) {
                    errorData = { error: `Erreur HTTP ${response.status}: ${response.statusText}` };
                }
            }

            console.error('Erreur serveur détaillée:', {
                status: response.status,
                statusText: response.statusText,
                errorData
            });

            alert(errorData.error || `Erreur lors de la suppression de l'image (${response.status})`);
        }
    } catch (error) {
        console.error('Erreur réseau complète:', error);
        const errorMessage = error instanceof Error ? error.message : String(error);
        alert(`Erreur de connexion lors de la suppression de l'image: ${errorMessage}`);
    } finally {
        isDeletingImage.value = null;
    }
}
</script>

<template>
    <Head title="Edit Place" />
    <AppLayout>

   
        <div class="container mx-auto p-4">
            <h1 class="text-2xl font-bold mb-4">Edit Place</h1>
            <form @submit.prevent="submit" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="name">Name</Label>
                        <Input id="name" type="text" v-model="form.name" />
                        <InputError :message="form.errors.name" />
                    </div>
                    <div>
                        <Label for="city">City</Label>
                        <Input id="city" type="text" v-model="form.city" />
                        <InputError :message="form.errors.city" />
                    </div>
                </div>

                <div>
                    <Label for="description">Description</Label>
                    <Textarea id="description" v-model="form.description" />
                    <InputError :message="form.errors.description" />
                </div>

                <div>
                    <Label for="location">Location</Label>
                    <Input id="location" type="text" v-model="form.location" />
                    <InputError :message="form.errors.location" />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="address">Address</Label>
                        <Input id="address" type="text" v-model="form.address" />
                        <InputError :message="form.errors.address" />
                    </div>
                    <div>
                        <Label for="neighborhood">Neighborhood</Label>
                        <Input id="neighborhood" type="text" v-model="form.neighborhood" />
                        <InputError :message="form.errors.neighborhood" />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="latitude">Latitude</Label>
                        <Input id="latitude" type="text" v-model="form.latitude" />
                        <InputError :message="form.errors.latitude" />
                    </div>
                    <div>
                        <Label for="longitude">Longitude</Label>
                        <Input id="longitude" type="text" v-model="form.longitude" />
                        <InputError :message="form.errors.longitude" />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="price">Price</Label>
                        <Input id="price" type="text" v-model="form.price" />
                        <InputError :message="form.errors.price" />
                    </div>
                    <div class="flex items-center space-x-2 mt-6">
                        <Checkbox id="is_free" v-model:checked="form.is_free" />
                        <Label for="is_free">Free</Label>
                        <InputError :message="form.errors.is_free" />
                    </div>
                </div>

                <div>
                    <Label for="opening_hours">Opening Hours</Label>
                    <Textarea id="opening_hours" v-model="form.opening_hours" />
                    <InputError :message="form.errors.opening_hours" />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="status">Status</Label>
                        <Select v-model="form.status">
                            <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                            </SelectContent>
                        </Select>
                        <InputError :message="form.errors.status" />
                    </div>
                    <div>
                        <Label for="categories">Categories</Label>
                        <select
                            id="categories"
                            v-model="form.categories"
                            multiple
                            class="flex w-full h-32 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            <option v-for="category in categories" :key="category.id" :value="category.id" class="p-2">
                                {{ category.name }}
                            </option>
                        </select>
                        <InputError :message="form.errors.categories" />
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <Label for="main_image_url">Image/Vidéo Principale</Label>
                        <div v-if="form.main_image_url || currentMainImageUrl" class="my-2">
                            <!-- Nouvelle image/vidéo sélectionnée -->
                            <div v-if="isMainImageFileSelected" class="relative group">
                                <p class="text-sm text-green-600 mb-1">Nouvelle image sélectionnée :</p>
                                <div v-if="mainImagePreviewUrl" class="relative">
                                    <video
                                        v-if="form.mediaType === 'video'"
                                        :src="mainImagePreviewUrl"
                                        class="w-32 h-32 object-cover rounded-md border-2 border-green-500"
                                        controls
                                        muted
                                    >
                                    </video>
                                    <img
                                        v-else
                                        :src="mainImagePreviewUrl"
                                        alt="New Main Image"
                                        class="w-32 h-32 object-cover rounded-md border-2 border-green-500"
                                    >
                                    <!-- Bouton de suppression pour nouvelle image -->
                                    <button
                                        @click="deleteMainImage"
                                        :disabled="isUploadingMainImage"
                                        class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600 disabled:opacity-50"
                                        title="Supprimer cette image"
                                    >
                                        ×
                                    </button>
                                </div>
                                <div v-else class="w-32 h-32 bg-gray-200 rounded-md flex items-center justify-center">
                                    <span class="text-gray-500 text-xs">Erreur aperçu</span>
                                </div>
                            </div>
                            <!-- Image/vidéo actuelle -->
                            <div v-else-if="currentMainImageUrl" class="relative group">
                                <p class="text-sm text-gray-600 mb-1">Image principale actuelle</p>
                                <div class="relative">
                                    <video
                                        v-if="currentMediaType === 'video'"
                                        :src="currentMainImageUrl"
                                        class="w-32 h-32 object-cover rounded-md border-2 border-gray-300"
                                        controls
                                        muted
                                    >
                                    </video>
                                    <img
                                        v-else
                                        :src="currentMainImageUrl"
                                        alt="Current Main Image"
                                        class="w-32 h-32 object-cover rounded-md border-2 border-gray-300"
                                    >
                                    <!-- Bouton de suppression pour image actuelle -->
                                    <button
                                        @click="deleteMainImage"
                                        :disabled="isUploadingMainImage"
                                        class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600 disabled:opacity-50"
                                        title="Supprimer l'image principale"
                                    >
                                        ×
                                    </button>
                                </div>
                            </div>
                        </div>
                        <Input
                            id="main_image_url"
                            type="file"
                            @change="handleMainImageUpload"
                            accept="image/*,video/*"
                            :disabled="isUploadingMainImage"
                            class="disabled:opacity-50"
                        />
                        <InputError :message="form.errors.main_image_url" />
                        <p class="text-xs text-gray-500 mt-1">
                            Formats acceptés: JPEG, PNG, GIF, WebP, SVG, MP4, MOV, AVI, WebM, OGG (max 100MB)
                        </p>
                    </div>
                    <div>
                        <Label for="mediaType">Media Type (Auto-detected: Image or Video)</Label>
                        <Input
                            id="mediaType"
                            type="text"
                            v-model="form.mediaType"
                            readonly
                            class="bg-gray-100 cursor-not-allowed"
                            placeholder="Upload a file to auto-detect type (image/video)"
                        />
                        <InputError :message="form.errors.mediaType" />
                    </div>
                </div>

                <div>
                    <Label for="images">Images/Videos</Label>

                    <!-- Images existantes -->
                    <div v-if="localImages && localImages.length" class="my-2">
                        <p class="text-sm text-gray-600 mb-2">Images actuelles ({{ localImages.length }}) :</p>
                        <div class="flex flex-wrap gap-2">
                            <div v-for="image in localImages" :key="image.id" class="relative group">
                                <img
                                    :src="image.image_url"
                                    :alt="`Image for ${props.place.name}`"
                                    class="w-32 h-32 object-cover rounded-md border-2 border-blue-300"
                                    :class="{ 'opacity-50': isDeletingImage === image.id }"
                                >
                                <!-- Indicateur de suppression -->
                                <div
                                    v-if="isDeletingImage === image.id"
                                    class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-md"
                                >
                                    <div class="text-white text-xs">Suppression...</div>
                                </div>
                                <!-- Bouton de suppression -->
                                <button
                                    @click="deleteImage(image.id)"
                                    :disabled="isDeletingImage === image.id"
                                    class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                                    title="Supprimer cette image"
                                >
                                    ×
                                </button>
                                <!-- ID de l'image -->
                                <div class="absolute bottom-0 left-0 bg-blue-500 text-white text-xs px-1 rounded-tr">
                                    ID: {{ image.id }}
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- Nouvelles images sélectionnées -->
                    <div v-if="form.images && form.images.length" class="my-2">
                        <p class="text-sm text-green-600 mb-2">Nouvelles images/vidéos sélectionnées ({{ form.images.length }}) :</p>
                        <div class="flex flex-wrap gap-2">
                            <div v-for="(image, index) in form.images" :key="'new-' + index" class="relative">
                                <video
                                    v-if="getMediaType(image) === 'video'"
                                    :src="createPreviewUrl(image)"
                                    class="w-32 h-32 object-cover rounded-md border-2 border-green-500"
                                    controls
                                    muted

                                >
                                </video>
                                <img
                                    v-else
                                    :src="createPreviewUrl(image)"
                                    :alt="`New image ${index + 1}`"
                                    class="w-32 h-32 object-cover rounded-md border-2 border-green-500"

                                >
                                <div class="absolute bottom-0 left-0 bg-green-500 text-white text-xs px-1 rounded-tr">
                                    {{ index + 1 }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <Input
                        id="images"
                        type="file"
                        @change="handleFileUpload"
                        multiple
                        accept="image/*,video/*"
                        :disabled="isUploadingImages"
                        class="disabled:opacity-50"
                    />
                    <InputError :message="form.errors.images" />
                    <p class="text-xs text-gray-500 mt-1">
                        Sélectionnez plusieurs fichiers. Formats acceptés: JPEG, PNG, GIF, WebP, SVG, MP4, MOV, AVI, WebM, OGG (max 100MB chacun)
                    </p>


                </div>

<div v-if="form.progress" class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                    <div class="bg-blue-600 h-2.5 rounded-full" :style="{ width: form.progress.percentage + '%' }"></div>
                </div>
                <Button type="submit" :disabled="form.processing">Update Place</Button>
            </form>
        </div>
    </AppLayout>
</template>