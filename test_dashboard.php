<?php

/**
 * Script de test automatique pour le Dashboard MbokaTour
 * 
 * Ce script teste toutes les fonctionnalités du dashboard
 * et vérifie que les données sont correctement affichées.
 */

require_once 'vendor/autoload.php';

use App\Http\Controllers\DashboardController;
use App\Models\Place;
use App\Models\User;
use App\Models\PlaceLike;
use App\Models\Comment;
use App\Models\UserPlaceFavorite;
use App\Models\Category;

// Initialiser Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "🧪 TEST DASHBOARD MBOKATOUR\n";
echo "==========================\n\n";

// Test 1: Vérifier les données de base
echo "📊 Test 1: Vérification des données de base\n";
echo "-------------------------------------------\n";

$stats = [
    'Lieux' => Place::count(),
    'Utilisateurs' => User::count(),
    'Likes' => PlaceLike::count(),
    'Commentaires' => Comment::count(),
    'Favoris' => UserPlaceFavorite::count(),
    'Catégories' => Category::count(),
];

foreach ($stats as $label => $count) {
    $status = $count > 0 ? '✅' : '❌';
    echo "{$status} {$label}: {$count}\n";
}

echo "\n";

// Test 2: Vérifier le contrôleur Dashboard
echo "🎛️ Test 2: Contrôleur Dashboard\n";
echo "-------------------------------\n";

try {
    $controller = new DashboardController();
    $response = $controller->index();
    
    echo "✅ Contrôleur instancié avec succès\n";
    echo "✅ Méthode index() exécutée\n";
    echo "✅ Type de réponse: " . get_class($response) . "\n";
    
    // Vérifier les données de la réponse
    $props = $response->toResponse(request())->getData()['page']['props'];
    
    if (isset($props['stats'])) {
        echo "✅ Statistiques présentes\n";
    } else {
        echo "❌ Statistiques manquantes\n";
    }
    
    if (isset($props['popularPlaces'])) {
        echo "✅ Lieux populaires présents (" . count($props['popularPlaces']) . ")\n";
    } else {
        echo "❌ Lieux populaires manquants\n";
    }
    
    if (isset($props['dailyActivity'])) {
        echo "✅ Activité quotidienne présente (" . count($props['dailyActivity']) . " jours)\n";
    } else {
        echo "❌ Activité quotidienne manquante\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur dans le contrôleur: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Vérifier les données des lieux populaires
echo "🏆 Test 3: Lieux populaires\n";
echo "---------------------------\n";

$popularPlaces = Place::orderBy('views_count', 'desc')->limit(5)->get();

if ($popularPlaces->count() > 0) {
    echo "✅ {$popularPlaces->count()} lieux populaires trouvés\n";
    
    foreach ($popularPlaces->take(3) as $index => $place) {
        echo "  " . ($index + 1) . ". {$place->name} ({$place->views_count} vues)\n";
    }
} else {
    echo "❌ Aucun lieu populaire trouvé\n";
}

echo "\n";

// Test 4: Vérifier les commentaires récents
echo "💬 Test 4: Commentaires récents\n";
echo "-------------------------------\n";

$recentComments = Comment::with(['user', 'place'])
    ->orderBy('created_at', 'desc')
    ->limit(5)
    ->get();

if ($recentComments->count() > 0) {
    echo "✅ {$recentComments->count()} commentaires récents trouvés\n";
    
    foreach ($recentComments->take(2) as $comment) {
        echo "  - {$comment->user->name} sur {$comment->place->name} ({$comment->rating}⭐)\n";
    }
} else {
    echo "❌ Aucun commentaire récent trouvé\n";
}

echo "\n";

// Test 5: Vérifier les utilisateurs actifs
echo "👥 Test 5: Utilisateurs actifs\n";
echo "------------------------------\n";

$activeUsers = User::withCount('placeLikes')
    ->orderBy('place_likes_count', 'desc')
    ->limit(5)
    ->get();

if ($activeUsers->count() > 0) {
    echo "✅ {$activeUsers->count()} utilisateurs actifs trouvés\n";
    
    foreach ($activeUsers->take(3) as $user) {
        echo "  - {$user->name} ({$user->place_likes_count} likes)\n";
    }
} else {
    echo "❌ Aucun utilisateur actif trouvé\n";
}

echo "\n";

// Test 6: Vérifier les catégories
echo "📂 Test 6: Statistiques des catégories\n";
echo "--------------------------------------\n";

$categoriesStats = Category::withCount('places')
    ->orderBy('places_count', 'desc')
    ->limit(5)
    ->get();

if ($categoriesStats->count() > 0) {
    echo "✅ {$categoriesStats->count()} catégories avec statistiques\n";
    
    foreach ($categoriesStats->take(3) as $category) {
        echo "  - {$category->name}: {$category->places_count} lieux\n";
    }
} else {
    echo "❌ Aucune catégorie avec statistiques trouvée\n";
}

echo "\n";

// Résumé final
echo "📋 RÉSUMÉ DES TESTS\n";
echo "==================\n";

$totalTests = 6;
$passedTests = 0;

// Compter les tests réussis (simplifié)
if (Place::count() > 0) $passedTests++;
if (User::count() > 0) $passedTests++;
if ($popularPlaces->count() > 0) $passedTests++;
if ($recentComments->count() > 0) $passedTests++;
if ($activeUsers->count() > 0) $passedTests++;
if ($categoriesStats->count() > 0) $passedTests++;

$percentage = round(($passedTests / $totalTests) * 100);

echo "Tests réussis: {$passedTests}/{$totalTests} ({$percentage}%)\n";

if ($percentage >= 80) {
    echo "🎉 Dashboard prêt pour la production!\n";
} elseif ($percentage >= 60) {
    echo "⚠️ Dashboard fonctionnel mais nécessite des améliorations\n";
} else {
    echo "❌ Dashboard nécessite des corrections importantes\n";
}

echo "\n";
echo "🌐 Accès au dashboard: http://127.0.0.1:8001/dashboard\n";
echo "🔑 Connexion admin: <EMAIL> / password123\n";
echo "\n";
echo "✨ Test terminé!\n";
