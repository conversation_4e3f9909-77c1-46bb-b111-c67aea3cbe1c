# Exemple de configuration Cloudinary pour MbokaTour
# Copiez ces variables dans votre fichier .env et remplacez par vos vraies valeurs

# Configuration Cloudinary
# Obtenez ces valeurs depuis votre dashboard Cloudinary : https://cloudinary.com/console
CLOUDINARY_CLOUD_NAME=your_actual_cloud_name
CLOUDINARY_API_KEY=your_actual_api_key
CLOUDINARY_API_SECRET=your_actual_api_secret
CLOUDINARY_SECURE=true
CLOUDINARY_UPLOAD_PRESET=
CLOUDINARY_NOTIFICATION_URL=

# Optionnel : Dossier par défaut pour organiser vos fichiers
CLOUDINARY_FOLDER=mbokatour

# Configuration du système de fichiers
# Changez vers 'cloudinary' pour utiliser Cloudinary comme stockage par défaut
FILESYSTEM_DISK=local

# Instructions :
# 1. Créez un compte sur https://cloudinary.com
# 2. Allez dans votre dashboard
# 3. Copiez les valeurs Cloud Name, API Key et API Secret
# 4. Remplacez les valeurs ci-dessus
# 5. Testez avec : php test_cloudinary_integration.php
