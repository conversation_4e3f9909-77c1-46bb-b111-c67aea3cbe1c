<?php

namespace Tests\Feature;

use App\Models\Place;
use App\Models\PlaceImage;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PlaceDeleteTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Fake the storage disk for testing
        Storage::fake('public');
    }

    public function test_deleting_place_removes_all_associated_files()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create fake files
        $mainImage = UploadedFile::fake()->image('main.jpg', 800, 600);
        $image1 = UploadedFile::fake()->image('image1.jpg', 400, 300);
        $image2 = UploadedFile::fake()->create('video.mp4', 5000, 'video/mp4');

        // Store the files to get their paths
        $mainImagePath = $mainImage->store('places/main', 'public');
        $image1Path = $image1->store('places/images', 'public');
        $image2Path = $image2->store('places/images', 'public');

        // Create a place with main image
        $place = Place::factory()->create([
            'main_image_url' => $mainImagePath,
            'mediaType' => 'image'
        ]);

        // Create associated images
        PlaceImage::create([
            'place_id' => $place->id,
            'image_url' => $image1Path
        ]);

        PlaceImage::create([
            'place_id' => $place->id,
            'image_url' => $image2Path
        ]);

        // Verify files exist before deletion
        Storage::disk('public')->assertExists($mainImagePath);
        Storage::disk('public')->assertExists($image1Path);
        Storage::disk('public')->assertExists($image2Path);

        // Verify database records exist
        $this->assertDatabaseHas('places', ['id' => $place->id]);
        $this->assertDatabaseHas('place_images', ['place_id' => $place->id]);
        $this->assertEquals(2, PlaceImage::where('place_id', $place->id)->count());

        // Delete the place
        $response = $this->delete(route('admin.places.destroy', $place->id));

        // Verify redirect
        $response->assertRedirect(route('admin.places.index'));

        // Verify place is deleted from database
        $this->assertDatabaseMissing('places', ['id' => $place->id]);

        // Verify associated images are deleted from database (cascade)
        $this->assertDatabaseMissing('place_images', ['place_id' => $place->id]);
        $this->assertEquals(0, PlaceImage::where('place_id', $place->id)->count());

        // Verify all files are deleted from storage
        Storage::disk('public')->assertMissing($mainImagePath);
        Storage::disk('public')->assertMissing($image1Path);
        Storage::disk('public')->assertMissing($image2Path);
    }

    public function test_deleting_place_without_images_works_correctly()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place without images
        $place = Place::factory()->create([
            'main_image_url' => null,
            'mediaType' => null
        ]);

        // Verify place exists
        $this->assertDatabaseHas('places', ['id' => $place->id]);

        // Delete the place
        $response = $this->delete(route('admin.places.destroy', $place->id));

        // Verify redirect
        $response->assertRedirect(route('admin.places.index'));

        // Verify place is deleted
        $this->assertDatabaseMissing('places', ['id' => $place->id]);
    }

    public function test_deleting_place_with_external_urls_works_correctly()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place with external URL (like Unsplash)
        $place = Place::factory()->create([
            'main_image_url' => 'https://images.unsplash.com/photo-example.jpg',
            'mediaType' => 'image'
        ]);

        // Create associated image with external URL
        PlaceImage::create([
            'place_id' => $place->id,
            'image_url' => 'https://images.unsplash.com/photo-example2.jpg'
        ]);

        // Verify place exists
        $this->assertDatabaseHas('places', ['id' => $place->id]);
        $this->assertDatabaseHas('place_images', ['place_id' => $place->id]);

        // Delete the place
        $response = $this->delete(route('admin.places.destroy', $place->id));

        // Verify redirect
        $response->assertRedirect(route('admin.places.index'));

        // Verify place and images are deleted from database
        $this->assertDatabaseMissing('places', ['id' => $place->id]);
        $this->assertDatabaseMissing('place_images', ['place_id' => $place->id]);

        // No files should be attempted to be deleted since they're external URLs
        // This test ensures no errors occur when trying to delete external URLs
    }
}
