<?php

namespace Tests\Feature\Api;

use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class CategoryControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_get_all_categories()
    {
        // Arrange: Create some categories
        $categories = Category::factory()->count(3)->create();

        // Act: Make a GET request to the categories endpoint
        $response = $this->getJson('/api/categories');

        // Assert: Check the response
        $response->assertStatus(200)
            ->assertJsonCount(3)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'name',
                    'type',
                    'icon',
                    'color',
                    'display_order',
                    'description',
                    'created_at',
                    'updated_at'
                ]
            ]);
    }

    public function test_categories_are_ordered_by_display_order()
    {
        // Arrange: Create categories with specific display orders
        $category1 = Category::factory()->create(['display_order' => 3, 'name' => 'Third']);
        $category2 = Category::factory()->create(['display_order' => 1, 'name' => 'First']);
        $category3 = Category::factory()->create(['display_order' => 2, 'name' => 'Second']);

        // Act: Make a GET request to the categories endpoint
        $response = $this->getJson('/api/categories');

        // Assert: Check that categories are ordered by display_order
        $response->assertStatus(200);
        
        $responseData = $response->json();
        $this->assertEquals('First', $responseData[0]['name']);
        $this->assertEquals('Second', $responseData[1]['name']);
        $this->assertEquals('Third', $responseData[2]['name']);
    }

    public function test_empty_categories_returns_empty_array()
    {
        // Act: Make a GET request when no categories exist
        $response = $this->getJson('/api/categories');

        // Assert: Check the response
        $response->assertStatus(200)
            ->assertJsonCount(0)
            ->assertExactJson([]);
    }
}
