<?php

namespace Tests\Feature\Api;

use App\Models\Place;
use App\Models\PlaceLike;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class PlaceLikeControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    public function test_user_can_like_a_place()
    {
        $user = User::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);

        Sanctum::actingAs($user);

        $response = $this->postJson('/api/places/likes/toggle', [
            'place_id' => $place->id
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Lieu liké avec succès.',
                'is_liked' => true,
                'likes_count' => 1,
            ]);

        $this->assertDatabaseHas('place_likes', [
            'user_id' => $user->id,
            'place_id' => $place->id,
        ]);
    }

    public function test_user_can_unlike_a_place()
    {
        $user = User::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);

        // Create a like first
        PlaceLike::create([
            'user_id' => $user->id,
            'place_id' => $place->id,
        ]);

        Sanctum::actingAs($user);

        $response = $this->postJson('/api/places/likes/toggle', [
            'place_id' => $place->id
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'message' => 'Like retiré avec succès.',
                'is_liked' => false,
                'likes_count' => 0,
            ]);

        $this->assertDatabaseMissing('place_likes', [
            'user_id' => $user->id,
            'place_id' => $place->id,
        ]);
    }

    public function test_user_can_get_like_status()
    {
        $user = User::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);

        PlaceLike::create([
            'user_id' => $user->id,
            'place_id' => $place->id,
        ]);

        Sanctum::actingAs($user);

        $response = $this->getJson("/api/places/{$place->id}/likes/status");

        $response->assertStatus(200)
            ->assertJson([
                'is_liked' => true,
                'likes_count' => 1,
            ]);
    }

    public function test_user_can_get_liked_places()
    {
        $user = User::factory()->create();
        $place1 = Place::factory()->create(['is_active' => true]);
        $place2 = Place::factory()->create(['is_active' => true]);

        // User likes place1
        PlaceLike::create([
            'user_id' => $user->id,
            'place_id' => $place1->id,
        ]);

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/user/likes/places');

        $response->assertStatus(200)
            ->assertJsonCount(1)
            ->assertJsonFragment([
                'id' => $place1->id,
                'name' => $place1->name,
            ]);
    }

    public function test_unauthenticated_user_cannot_like_place()
    {
        $place = Place::factory()->create(['is_active' => true]);

        $response = $this->postJson('/api/places/likes/toggle', [
            'place_id' => $place->id
        ]);

        $response->assertStatus(401);
    }

    public function test_like_toggle_validates_place_id()
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $response = $this->postJson('/api/places/likes/toggle', [
            'place_id' => 999999 // Non-existent place
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['place_id']);
    }
}
