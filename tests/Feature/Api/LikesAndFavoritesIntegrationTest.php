<?php

namespace Tests\Feature\Api;

use App\Models\Place;
use App\Models\PlaceLike;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class LikesAndFavoritesIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate');
    }

    public function test_discover_endpoint_includes_both_likes_and_favorites_data()
    {
        $user = User::factory()->create();
        $place1 = Place::factory()->create(['is_active' => true, 'name' => 'Place 1']);
        $place2 = Place::factory()->create(['is_active' => true, 'name' => 'Place 2']);
        $place3 = Place::factory()->create(['is_active' => true, 'name' => 'Place 3']);

        // User likes place1 and place2
        PlaceLike::create(['user_id' => $user->id, 'place_id' => $place1->id]);
        PlaceLike::create(['user_id' => $user->id, 'place_id' => $place2->id]);

        // User favorites place2 and place3
        $user->favoritePlaces()->attach([$place2->id, $place3->id]);

        // Another user likes place1 (to test likes count)
        $otherUser = User::factory()->create();
        PlaceLike::create(['user_id' => $otherUser->id, 'place_id' => $place1->id]);

        Sanctum::actingAs($user);

        $response = $this->getJson('/api/places/discover');

        $response->assertStatus(200);

        $places = $response->json();

        // Find each place in the response
        $place1Data = collect($places)->firstWhere('id', $place1->id);
        $place2Data = collect($places)->firstWhere('id', $place2->id);
        $place3Data = collect($places)->firstWhere('id', $place3->id);

        // Assertions for place1 (liked by user, not favorited, 2 total likes)
        $this->assertTrue($place1Data['is_liked']);
        $this->assertFalse($place1Data['is_favorited']);
        $this->assertEquals(2, $place1Data['likes_count']);

        // Assertions for place2 (liked and favorited by user, 1 total like)
        $this->assertTrue($place2Data['is_liked']);
        $this->assertTrue($place2Data['is_favorited']);
        $this->assertEquals(1, $place2Data['likes_count']);

        // Assertions for place3 (not liked, favorited by user, 0 total likes)
        $this->assertFalse($place3Data['is_liked']);
        $this->assertTrue($place3Data['is_favorited']);
        $this->assertEquals(0, $place3Data['likes_count']);
    }

    public function test_show_endpoint_includes_both_likes_and_favorites_data()
    {
        $user = User::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);

        // User likes and favorites the place
        PlaceLike::create(['user_id' => $user->id, 'place_id' => $place->id]);
        $user->favoritePlaces()->attach($place->id);

        // Another user likes the place
        $otherUser = User::factory()->create();
        PlaceLike::create(['user_id' => $otherUser->id, 'place_id' => $place->id]);

        Sanctum::actingAs($user);

        $response = $this->getJson("/api/places/{$place->id}");

        $response->assertStatus(200)
            ->assertJson([
                'id' => $place->id,
                'is_liked' => true,
                'is_favorited' => true,
                'likes_count' => 2,
            ]);
    }

    public function test_guest_user_sees_default_values_for_likes_and_favorites()
    {
        $place = Place::factory()->create(['is_active' => true]);

        // Create some likes from other users
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        PlaceLike::create(['user_id' => $user1->id, 'place_id' => $place->id]);
        PlaceLike::create(['user_id' => $user2->id, 'place_id' => $place->id]);

        // Test discover endpoint
        $response = $this->getJson('/api/places/discover');
        $response->assertStatus(200);

        $placeData = collect($response->json())->firstWhere('id', $place->id);
        $this->assertFalse($placeData['is_liked']);
        $this->assertFalse($placeData['is_favorited']);
        $this->assertEquals(2, $placeData['likes_count']);

        // Test show endpoint
        $response = $this->getJson("/api/places/{$place->id}");
        $response->assertStatus(200)
            ->assertJson([
                'id' => $place->id,
                'is_liked' => false,
                'is_favorited' => false,
                'likes_count' => 2,
            ]);
    }

    public function test_user_can_manage_likes_and_favorites_independently()
    {
        $user = User::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);

        Sanctum::actingAs($user);

        // Like the place
        $response = $this->postJson('/api/places/likes/toggle', ['place_id' => $place->id]);
        $response->assertJson(['is_liked' => true]);

        // Add to favorites
        $response = $this->postJson('/api/user/favorites/places', ['place_id' => $place->id]);
        $response->assertStatus(200);

        // Check both are set
        $response = $this->getJson("/api/places/{$place->id}");
        $response->assertJson([
            'is_liked' => true,
            'is_favorited' => true,
        ]);

        // Unlike (but keep in favorites)
        $response = $this->postJson('/api/places/likes/toggle', ['place_id' => $place->id]);
        $response->assertJson(['is_liked' => false]);

        // Check status
        $response = $this->getJson("/api/places/{$place->id}");
        $response->assertJson([
            'is_liked' => false,
            'is_favorited' => true,
        ]);

        // Remove from favorites (like status should remain false)
        $response = $this->deleteJson("/api/user/favorites/places/{$place->id}");
        $response->assertStatus(200);

        // Check final status
        $response = $this->getJson("/api/places/{$place->id}");
        $response->assertJson([
            'is_liked' => false,
            'is_favorited' => false,
        ]);
    }
}
