<?php

namespace Tests\Feature\Api;

use App\Models\Category;
use App\Models\Place;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PlaceNearbyTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test categories
        Category::factory()->create(['id' => 1, 'name' => 'Nature & Évasion']);
        Category::factory()->create(['id' => 2, 'name' => 'Restaurants']);
    }

    public function test_nearby_endpoint_returns_places_within_radius()
    {
        // Create places at different distances from Kinshasa center (-4.3217, 15.3125)
        $nearPlace = Place::factory()->create([
            'name' => 'Near Place',
            'latitude' => -4.3217, // Same coordinates as search point
            'longitude' => 15.3125,
            'is_active' => true
        ]);

        $farPlace = Place::factory()->create([
            'name' => 'Far Place',
            'latitude' => -4.5000, // About 20km away
            'longitude' => 15.5000,
            'is_active' => true
        ]);

        // Search with 5km radius
        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=5');

        $response->assertStatus(200);
        $data = $response->json();

        // Should only return the near place
        $this->assertCount(1, $data);
        $this->assertEquals('Near Place', $data[0]['name']);
        $this->assertArrayHasKey('distance', $data[0]);
        $this->assertEquals(0, $data[0]['distance']); // Same coordinates = 0 distance
    }

    public function test_nearby_endpoint_validates_required_parameters()
    {
        $response = $this->getJson('/api/places/nearby');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['latitude', 'longitude']);
    }

    public function test_nearby_endpoint_validates_latitude_range()
    {
        $response = $this->getJson('/api/places/nearby?latitude=100&longitude=15.3125');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['latitude']);
    }

    public function test_nearby_endpoint_validates_longitude_range()
    {
        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=200');

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['longitude']);
    }

    public function test_nearby_endpoint_uses_default_radius()
    {
        Place::factory()->create([
            'latitude' => -4.3217,
            'longitude' => 15.3125,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125');

        $response->assertStatus(200);
        // Should work with default radius (20km)
    }

    public function test_nearby_endpoint_sorts_by_distance()
    {
        // Create places at different distances
        $place1 = Place::factory()->create([
            'name' => 'Closest',
            'latitude' => -4.3217,
            'longitude' => 15.3125,
            'is_active' => true
        ]);

        $place2 = Place::factory()->create([
            'name' => 'Middle',
            'latitude' => -4.3250,
            'longitude' => 15.3150,
            'is_active' => true
        ]);

        $place3 = Place::factory()->create([
            'name' => 'Farthest',
            'latitude' => -4.3300,
            'longitude' => 15.3200,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=10');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(3, $data);
        
        // Should be sorted by distance (closest first)
        $this->assertEquals('Closest', $data[0]['name']);
        $this->assertEquals('Middle', $data[1]['name']);
        $this->assertEquals('Farthest', $data[2]['name']);

        // Distances should be in ascending order
        $this->assertLessThanOrEqual($data[1]['distance'], $data[0]['distance']);
        $this->assertLessThanOrEqual($data[2]['distance'], $data[1]['distance']);
    }

    public function test_nearby_endpoint_includes_required_fields()
    {
        $category = Category::factory()->create();
        $place = Place::factory()->create([
            'latitude' => -4.3217,
            'longitude' => 15.3125,
            'is_active' => true,
            'mediaType' => 'image'
        ]);
        $place->categories()->attach($category);

        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=5');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(1, $data);
        $place = $data[0];

        // Check all required fields are present
        $requiredFields = [
            'id', 'name', 'description', 'latitude', 'longitude', 'main_image_url',
            'media_type', 'address', 'neighborhood', 'city', 'price', 'is_free',
            'opening_hours', 'status', 'is_active', 'is_featured', 'views_count',
            'priority', 'is_liked', 'is_favorited', 'likes_count', 'categories', 'images'
        ];

        foreach ($requiredFields as $field) {
            $this->assertArrayHasKey($field, $place, "Missing field: {$field}");
        }
    }

    public function test_nearby_endpoint_excludes_inactive_places()
    {
        Place::factory()->create([
            'latitude' => -4.3217,
            'longitude' => 15.3125,
            'is_active' => false
        ]);

        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=5');

        $response->assertStatus(200);
        $this->assertCount(0, $response->json());
    }

    public function test_nearby_endpoint_excludes_places_without_coordinates()
    {
        Place::factory()->create([
            'latitude' => null,
            'longitude' => null,
            'is_active' => true
        ]);

        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=5');

        $response->assertStatus(200);
        $this->assertCount(0, $response->json());
    }

    public function test_nearby_endpoint_includes_user_favorites_when_authenticated()
    {
        $user = User::factory()->create();
        $place = Place::factory()->create([
            'latitude' => -4.3217,
            'longitude' => 15.3125,
            'is_active' => true
        ]);

        // Add place to user's favorites
        $user->favoritePlaces()->attach($place);

        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=5');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertCount(1, $data);
        $this->assertTrue($data[0]['is_favorited']);
    }

    public function test_nearby_endpoint_returns_empty_array_when_no_places_found()
    {
        // No places created
        $response = $this->getJson('/api/places/nearby?latitude=-4.3217&longitude=15.3125&radius=5');

        $response->assertStatus(200);
        $this->assertEquals([], $response->json());
    }
}
