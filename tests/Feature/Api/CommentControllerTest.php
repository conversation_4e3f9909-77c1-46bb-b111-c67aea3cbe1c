<?php

namespace Tests\Feature\Api;

use App\Models\Comment;
use App\Models\Place;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class CommentControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_get_comments_for_place()
    {
        $place = Place::factory()->create();
        $user = User::factory()->create();
        
        // Create some comments
        Comment::factory()->count(3)->create([
            'place_id' => $place->id,
            'user_id' => $user->id,
            'is_approved' => true,
        ]);

        $response = $this->getJson("/api/places/{$place->id}/comments");

        $response->assertStatus(200)
            ->assertJsonCount(3)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'content',
                    'rating',
                    'created_at',
                    'user' => [
                        'id',
                        'name'
                    ]
                ]
            ]);
    }

    public function test_can_create_comment_when_authenticated()
    {
        $place = Place::factory()->create();
        $user = User::factory()->create();
        
        Sanctum::actingAs($user);

        $commentData = [
            'content' => 'Great place to visit!',
            'rating' => 5,
        ];

        $response = $this->postJson("/api/places/{$place->id}/comments", $commentData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'comment' => [
                    'id',
                    'content',
                    'rating',
                    'created_at',
                    'user' => [
                        'id',
                        'name'
                    ]
                ]
            ]);

        $this->assertDatabaseHas('comments', [
            'place_id' => $place->id,
            'user_id' => $user->id,
            'content' => 'Great place to visit!',
            'rating' => 5,
        ]);
    }

    public function test_cannot_create_comment_when_not_authenticated()
    {
        $place = Place::factory()->create();

        $commentData = [
            'content' => 'Great place to visit!',
            'rating' => 5,
        ];

        $response = $this->postJson("/api/places/{$place->id}/comments", $commentData);

        $response->assertStatus(401);
    }

    public function test_cannot_create_duplicate_comment_for_same_place()
    {
        $place = Place::factory()->create();
        $user = User::factory()->create();
        
        // Create existing comment
        Comment::factory()->create([
            'place_id' => $place->id,
            'user_id' => $user->id,
        ]);

        Sanctum::actingAs($user);

        $commentData = [
            'content' => 'Another comment',
            'rating' => 4,
        ];

        $response = $this->postJson("/api/places/{$place->id}/comments", $commentData);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['comment']);
    }

    public function test_can_update_own_comment()
    {
        $place = Place::factory()->create();
        $user = User::factory()->create();
        
        $comment = Comment::factory()->create([
            'place_id' => $place->id,
            'user_id' => $user->id,
            'content' => 'Original content',
            'rating' => 3,
        ]);

        Sanctum::actingAs($user);

        $updateData = [
            'content' => 'Updated content',
            'rating' => 5,
        ];

        $response = $this->putJson("/api/places/{$place->id}/comments/{$comment->id}", $updateData);

        $response->assertStatus(200)
            ->assertJsonPath('comment.content', 'Updated content')
            ->assertJsonPath('comment.rating', 5);

        $this->assertDatabaseHas('comments', [
            'id' => $comment->id,
            'content' => 'Updated content',
            'rating' => 5,
        ]);
    }

    public function test_cannot_update_other_users_comment()
    {
        $place = Place::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $comment = Comment::factory()->create([
            'place_id' => $place->id,
            'user_id' => $user1->id,
        ]);

        Sanctum::actingAs($user2);

        $updateData = [
            'content' => 'Trying to update',
            'rating' => 1,
        ];

        $response = $this->putJson("/api/places/{$place->id}/comments/{$comment->id}", $updateData);

        $response->assertStatus(404);
    }

    public function test_can_delete_own_comment()
    {
        $place = Place::factory()->create();
        $user = User::factory()->create();
        
        $comment = Comment::factory()->create([
            'place_id' => $place->id,
            'user_id' => $user->id,
        ]);

        Sanctum::actingAs($user);

        $response = $this->deleteJson("/api/places/{$place->id}/comments/{$comment->id}");

        $response->assertStatus(200)
            ->assertJsonPath('message', 'Commentaire supprimé avec succès.');

        $this->assertDatabaseMissing('comments', [
            'id' => $comment->id,
        ]);
    }

    public function test_cannot_delete_other_users_comment()
    {
        $place = Place::factory()->create();
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $comment = Comment::factory()->create([
            'place_id' => $place->id,
            'user_id' => $user1->id,
        ]);

        Sanctum::actingAs($user2);

        $response = $this->deleteJson("/api/places/{$place->id}/comments/{$comment->id}");

        $response->assertStatus(404);
        
        $this->assertDatabaseHas('comments', [
            'id' => $comment->id,
        ]);
    }

    public function test_place_show_includes_comments()
    {
        $place = Place::factory()->create();
        $user = User::factory()->create();
        
        Comment::factory()->count(2)->create([
            'place_id' => $place->id,
            'user_id' => $user->id,
            'is_approved' => true,
        ]);

        $response = $this->getJson("/api/places/{$place->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'name',
                'comments' => [
                    '*' => [
                        'id',
                        'content',
                        'rating',
                        'user' => [
                            'id',
                            'name'
                        ]
                    ]
                ]
            ]);
    }
}
