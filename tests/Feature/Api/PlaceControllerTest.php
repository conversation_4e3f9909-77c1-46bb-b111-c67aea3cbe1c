<?php

namespace Tests\Feature\Api;

use App\Models\Category;
use App\Models\Place;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PlaceControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_discover_endpoint_returns_places()
    {
        Place::factory()->count(5)->create(['is_active' => true]);

        $response = $this->getJson('/api/places/discover');

        $response->assertStatus(200)
            ->assertJsonCount(5);
    }

    public function test_discover_endpoint_respects_limit_parameter()
    {
        Place::factory()->count(10)->create(['is_active' => true]);

        $response = $this->getJson('/api/places/discover?limit=3');

        $response->assertStatus(200)
            ->assertJsonCount(3);
    }

    public function test_discover_endpoint_respects_max_limit()
    {
        Place::factory()->count(100)->create(['is_active' => true]);

        $response = $this->getJson('/api/places/discover?limit=100');

        $response->assertStatus(200)
            ->assertJsonCount(50); // Max limit is 50
    }

    public function test_discover_endpoint_filters_by_category()
    {
        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        $place1 = Place::factory()->create(['is_active' => true]);
        $place2 = Place::factory()->create(['is_active' => true]);

        $place1->categories()->attach($category1);
        $place2->categories()->attach($category2);

        $response = $this->getJson("/api/places/discover?category_id={$category1->id}");

        $response->assertStatus(200)
            ->assertJsonCount(1)
            ->assertJsonPath('0.id', $place1->id);
    }

    public function test_discover_endpoint_includes_favorites_for_authenticated_user()
    {
        /** @var User $user */
        $user = User::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);
        $user->favoritePlaces()->attach($place);

        $this->actingAs($user);

        $response = $this->getJson('/api/places/discover');

        $response->assertStatus(200)
            ->assertJsonPath('0.is_favorited', true);
    }

    public function test_discover_endpoint_does_not_include_favorites_for_guest()
    {
        Place::factory()->create(['is_active' => true]);

        $response = $this->getJson('/api/places/discover');

        $response->assertStatus(200)
            ->assertJsonPath('0.is_favorited', false);
    }

    public function test_discover_endpoint_includes_required_relationships()
    {
        $category = Category::factory()->create(['name' => 'Test Category']);
        $place = Place::factory()->create(['is_active' => true]);
        $place->categories()->attach($category);

        $response = $this->getJson('/api/places/discover');

        $response->assertStatus(200)
            ->assertJsonStructure([
                '*' => [
                    'id',
                    'name',
                    'description',
                    'location',
                    'price',
                    'is_free',
                    'latitude',
                    'longitude',
                    'address',
                    'neighborhood',
                    'city',
                    'main_image_url',
                    'mediaType',
                    'is_featured',
                    'views_count',
                    'priority',
                    'is_favorited',
                    'is_liked',
                    'likes_count',
                    'categories' => [
                        '*' => [
                            'id',
                            'name',
                            'icon',
                            'color'
                        ]
                    ],
                    'images'
                ]
            ]);
    }

    public function test_show_endpoint_increments_views_count()
    {
        $place = Place::factory()->create(['is_active' => true, 'views_count' => 5]);

        // Get initial views count
        $initialViews = $place->views_count;

        // Make request to show endpoint
        $response = $this->getJson("/api/places/{$place->id}");

        // Assert response is successful
        $response->assertStatus(200)
            ->assertJsonPath('id', $place->id);

        // Refresh the place from database and check views count increased
        $place->refresh();
        $this->assertEquals($initialViews + 1, $place->views_count);
    }

    public function test_show_endpoint_increments_views_on_multiple_requests()
    {
        $place = Place::factory()->create(['is_active' => true, 'views_count' => 0]);

        // Make multiple requests
        $this->getJson("/api/places/{$place->id}");
        $this->getJson("/api/places/{$place->id}");
        $this->getJson("/api/places/{$place->id}");

        // Check views count increased by 3
        $place->refresh();
        $this->assertEquals(3, $place->views_count);
    }
}
