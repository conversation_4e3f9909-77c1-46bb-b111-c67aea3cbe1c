<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Place;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class PlaceUpdateTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    public function test_can_update_place_with_images_via_post()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place
        $place = Place::factory()->create([
            'name' => 'Original Place',
            'description' => 'Original description',
            'location' => 'Original location',
            'status' => 'active',
        ]);

        // Create categories
        $category1 = Category::factory()->create();
        $category2 = Category::factory()->create();

        // Create fake files
        $mainImage = UploadedFile::fake()->image('main.jpg', 800, 600)->size(1024); // 1MB
        $image1 = UploadedFile::fake()->image('image1.jpg', 400, 300)->size(512); // 512KB
        $image2 = UploadedFile::fake()->image('image2.png', 400, 300)->size(512); // 512KB

        // Prepare update data
        $updateData = [
            'name' => 'Updated Place',
            'description' => 'Updated description',
            'location' => 'Updated location',
            'address' => 'Updated address',
            'neighborhood' => 'Updated neighborhood',
            'city' => 'Updated city',
            'latitude' => 12.345,
            'longitude' => 67.890,
            'price' => 25.50,
            'opening_hours' => '9:00 AM - 6:00 PM',
            'is_free' => 0,
            'status' => 'active',
            'main_image_url' => $mainImage,
            'mediaType' => 'image',
            'images' => [$image1, $image2],
            'categories' => [$category1->id, $category2->id],
        ];

        // Make POST request to update the place
        $response = $this->post(route('admin.places.update', $place->id), $updateData);

        // Assert successful redirect
        $response->assertRedirect(route('admin.places.index'));

        // Refresh the place from database
        $place->refresh();

        // Assert place data was updated
        $this->assertEquals('Updated Place', $place->name);
        $this->assertEquals('Updated description', $place->description);
        $this->assertEquals('Updated location', $place->location);
        $this->assertEquals('Updated address', $place->address);
        $this->assertEquals('Updated neighborhood', $place->neighborhood);
        $this->assertEquals('Updated city', $place->city);
        $this->assertEquals(12.345, $place->latitude);
        $this->assertEquals(67.890, $place->longitude);
        $this->assertEquals(25.50, $place->price);
        $this->assertEquals('9:00 AM - 6:00 PM', $place->opening_hours);
        $this->assertEquals(false, $place->is_free);
        $this->assertEquals('active', $place->status);
        $this->assertEquals('image', $place->mediaType);

        // Assert categories were attached
        $this->assertCount(2, $place->categories);
        $this->assertTrue($place->categories->contains($category1));
        $this->assertTrue($place->categories->contains($category2));

        // Assert main image was uploaded
        $this->assertNotNull($place->main_image_url);

        // Assert additional images were uploaded
        $this->assertCount(2, $place->images);
    }

    public function test_can_update_place_with_video_as_main_media()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place
        $place = Place::factory()->create([
            'name' => 'Original Place',
            'mediaType' => 'image',
        ]);

        // Create fake video file
        $mainVideo = UploadedFile::fake()->create('main.mp4', 5120, 'video/mp4'); // 5MB

        // Prepare update data
        $updateData = [
            'name' => 'Updated Place with Video',
            'description' => 'Updated description',
            'location' => 'Updated location',
            'status' => 'active',
            'main_image_url' => $mainVideo,
            'mediaType' => 'video',
        ];

        // Make POST request to update the place
        $response = $this->post(route('admin.places.update', $place->id), $updateData);

        // Assert successful redirect
        $response->assertRedirect(route('admin.places.index'));

        // Refresh the place from database
        $place->refresh();

        // Assert place data was updated
        $this->assertEquals('Updated Place with Video', $place->name);
        $this->assertEquals('video', $place->mediaType);

        // Assert main video was uploaded
        $this->assertNotNull($place->main_image_url);
    }

    public function test_validates_file_size_limits()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place
        $place = Place::factory()->create();

        // Create fake file that's too large (over 100MB)
        $largeFile = UploadedFile::fake()->create('large.jpg', 110 * 1024, 'image/jpeg'); // 110MB

        // Prepare update data with oversized file
        $updateData = [
            'name' => 'Updated Place',
            'description' => 'Updated description',
            'location' => 'Updated location',
            'status' => 'active',
            'main_image_url' => $largeFile,
        ];

        // Make POST request to update the place
        $response = $this->post(route('admin.places.update', $place->id), $updateData);

        // Assert validation error
        $response->assertSessionHasErrors(['main_image_url']);
    }

    public function test_validates_required_fields()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place
        $place = Place::factory()->create();

        // Prepare update data with missing required fields
        $updateData = [
            'name' => '', // Empty name should fail validation
            'description' => '',
            'location' => '',
        ];

        // Make POST request to update the place
        $response = $this->post(route('admin.places.update', $place->id), $updateData);

        // Assert validation errors
        $response->assertSessionHasErrors(['name', 'description', 'location']);
    }

    public function test_can_delete_place_image()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place with an image
        $place = Place::factory()->create();

        // Create a fake image file and store it
        Storage::fake('public');
        $imagePath = 'places/images/test-image.jpg';
        Storage::disk('public')->put($imagePath, 'fake-image-content');

        // Create a place image record
        $placeImage = $place->images()->create([
            'image_url' => $imagePath,
        ]);

        // Verify the image exists
        $this->assertTrue(Storage::disk('public')->exists($imagePath));
        $this->assertDatabaseHas('place_images', [
            'id' => $placeImage->id,
            'place_id' => $place->id,
        ]);

        // Delete the image
        $response = $this->delete(route('admin.places.images.delete', [
            'place' => $place->id,
            'image' => $placeImage->id,
        ]));

        // Assert successful response
        $response->assertOk();
        $response->assertJson(['success' => 'Image deleted successfully']);

        // Assert image was deleted from storage
        $this->assertFalse(Storage::disk('public')->exists($imagePath));

        // Assert image record was deleted from database
        $this->assertDatabaseMissing('place_images', [
            'id' => $placeImage->id,
        ]);
    }

    public function test_cannot_delete_image_from_different_place()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create two places
        $place1 = Place::factory()->create();
        $place2 = Place::factory()->create();

        // Create an image for place2
        $placeImage = $place2->images()->create([
            'image_url' => 'places/images/test-image.jpg',
        ]);

        // Try to delete place2's image via place1's route
        $response = $this->delete(route('admin.places.images.delete', [
            'place' => $place1->id,
            'image' => $placeImage->id,
        ]));

        // Assert error response
        $response->assertStatus(404);
        $response->assertJson(['error' => 'Image not found for this place']);

        // Assert image still exists
        $this->assertDatabaseHas('place_images', [
            'id' => $placeImage->id,
        ]);
    }

    public function test_main_image_persists_when_deleting_other_images()
    {
        // Create a user and authenticate
        $user = User::factory()->create();
        $this->actingAs($user);

        // Create a place with main image and additional images
        Storage::fake('public');

        $mainImagePath = 'places/main/main-image.jpg';
        Storage::disk('public')->put($mainImagePath, 'fake-main-image-content');

        $place = Place::factory()->create([
            'main_image_url' => $mainImagePath,
            'mediaType' => 'image',
        ]);

        // Create additional images
        $additionalImagePath = 'places/images/additional-image.jpg';
        Storage::disk('public')->put($additionalImagePath, 'fake-additional-image-content');

        $additionalImage = $place->images()->create([
            'image_url' => $additionalImagePath,
        ]);

        // Verify both images exist
        $this->assertTrue(Storage::disk('public')->exists($mainImagePath));
        $this->assertTrue(Storage::disk('public')->exists($additionalImagePath));
        $this->assertDatabaseHas('places', [
            'id' => $place->id,
            'main_image_url' => $mainImagePath,
        ]);
        $this->assertDatabaseHas('place_images', [
            'id' => $additionalImage->id,
        ]);

        // Delete the additional image
        $response = $this->delete(route('admin.places.images.delete', [
            'place' => $place->id,
            'image' => $additionalImage->id,
        ]));

        // Assert successful response
        $response->assertOk();

        // Refresh place from database
        $place->refresh();

        // Assert main image still exists and is unchanged
        $this->assertNotNull($place->main_image_url);
        $this->assertEquals($mainImagePath, $place->getAttributes()['main_image_url']);
        $this->assertEquals('image', $place->mediaType);
        $this->assertTrue(Storage::disk('public')->exists($mainImagePath));

        // Assert additional image was deleted
        $this->assertFalse(Storage::disk('public')->exists($additionalImagePath));
        $this->assertDatabaseMissing('place_images', [
            'id' => $additionalImage->id,
        ]);
    }
}
