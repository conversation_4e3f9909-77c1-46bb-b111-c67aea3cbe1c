<?php

namespace Tests\Feature;

use App\Models\Place;
use App\Models\User;
use App\Repositories\PlaceRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UserFavoritesIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_favorites_work_correctly_in_discover_places()
    {
        // Create test data
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $place1 = Place::factory()->create(['is_active' => true, 'name' => 'Place 1']);
        $place2 = Place::factory()->create(['is_active' => true, 'name' => 'Place 2']);
        $place3 = Place::factory()->create(['is_active' => true, 'name' => 'Place 3']);

        // User1 favorites place1 and place3
        $user1->favoritePlaces()->attach([$place1->id, $place3->id]);
        
        // User2 favorites only place2
        $user2->favoritePlaces()->attach($place2->id);

        $repository = new PlaceRepository();

        // Test for user1
        $placesForUser1 = $repository->getDiscoverPlaces(['user_id' => $user1->id]);
        
        $place1ForUser1 = $placesForUser1->where('id', $place1->id)->first();
        $place2ForUser1 = $placesForUser1->where('id', $place2->id)->first();
        $place3ForUser1 = $placesForUser1->where('id', $place3->id)->first();

        // Assertions for user1
        $this->assertNotNull($place1ForUser1);
        $this->assertNotNull($place2ForUser1);
        $this->assertNotNull($place3ForUser1);
        
        $this->assertTrue($place1ForUser1->is_favorited, 'Place 1 should be favorited by user1');
        $this->assertFalse($place2ForUser1->is_favorited, 'Place 2 should NOT be favorited by user1');
        $this->assertTrue($place3ForUser1->is_favorited, 'Place 3 should be favorited by user1');

        // Test for user2
        $placesForUser2 = $repository->getDiscoverPlaces(['user_id' => $user2->id]);
        
        $place1ForUser2 = $placesForUser2->where('id', $place1->id)->first();
        $place2ForUser2 = $placesForUser2->where('id', $place2->id)->first();
        $place3ForUser2 = $placesForUser2->where('id', $place3->id)->first();

        // Assertions for user2
        $this->assertNotNull($place1ForUser2);
        $this->assertNotNull($place2ForUser2);
        $this->assertNotNull($place3ForUser2);
        
        $this->assertFalse($place1ForUser2->is_favorited, 'Place 1 should NOT be favorited by user2');
        $this->assertTrue($place2ForUser2->is_favorited, 'Place 2 should be favorited by user2');
        $this->assertFalse($place3ForUser2->is_favorited, 'Place 3 should NOT be favorited by user2');

        // Test without user (guest)
        $placesForGuest = $repository->getDiscoverPlaces();
        
        foreach ($placesForGuest as $place) {
            $this->assertFalse($place->is_favorited, "Place {$place->name} should not be favorited for guest");
        }
    }

    public function test_api_endpoint_respects_user_favorites()
    {
        /** @var User $user */
        $user = User::factory()->create(['email' => 'testapi' . time() . '@example.com']);
        $place1 = Place::factory()->create(['is_active' => true, 'name' => 'API Test Place 1']);
        $place2 = Place::factory()->create(['is_active' => true, 'name' => 'API Test Place 2']);

        // User favorites place1
        $user->favoritePlaces()->attach($place1->id);

        // Test as authenticated user
        $response = $this->actingAs($user)->getJson('/api/places/discover');

        $response->assertStatus(200);

        $places = $response->json();
        $favoritedPlace = collect($places)->where('id', $place1->id)->first();
        $nonFavoritedPlace = collect($places)->where('id', $place2->id)->first();

        $this->assertNotNull($favoritedPlace, 'Favorited place should be in results');
        $this->assertNotNull($nonFavoritedPlace, 'Non-favorited place should be in results');
        $this->assertTrue($favoritedPlace['is_favorited'], 'Place should be marked as favorited');
        $this->assertFalse($nonFavoritedPlace['is_favorited'], 'Place should not be marked as favorited');

        // Test as guest - ensure user is logged out
        $this->app['auth']->logout();
        $this->assertGuest();

        $guestResponse = $this->getJson('/api/places/discover');
        $guestResponse->assertStatus(200);

        $guestPlaces = $guestResponse->json();
        $guestPlace1 = collect($guestPlaces)->where('id', $place1->id)->first();
        $guestPlace2 = collect($guestPlaces)->where('id', $place2->id)->first();

        $this->assertNotNull($guestPlace1, 'Place 1 should be in guest results');
        $this->assertNotNull($guestPlace2, 'Place 2 should be in guest results');
        $this->assertFalse($guestPlace1['is_favorited'], "Place {$place1->name} should not be favorited for guest");
        $this->assertFalse($guestPlace2['is_favorited'], "Place {$place2->name} should not be favorited for guest");
    }
}
