<?php

namespace Tests\Feature;

use App\Models\Category;
use App\Models\Place;
use App\Models\User;
use App\Repositories\PlaceRepository;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PlaceRepositoryTest extends TestCase
{
    use RefreshDatabase;

    protected PlaceRepository $repository;

    protected function setUp(): void
    {
        parent::setUp();
        $this->repository = new PlaceRepository();
    }

    public function test_get_discover_places_returns_active_places_only()
    {
        // Create active and inactive places
        Place::factory()->create(['is_active' => true, 'name' => 'Active Place']);
        Place::factory()->create(['is_active' => false, 'name' => 'Inactive Place']);

        $places = $this->repository->getDiscoverPlaces();

        $this->assertCount(1, $places);
        $this->assertEquals('Active Place', $places->first()->name);
    }

    public function test_get_discover_places_respects_limit_option()
    {
        Place::factory()->count(30)->create(['is_active' => true]);

        $places = $this->repository->getDiscoverPlaces(['limit' => 10]);

        $this->assertCount(10, $places);
    }

    public function test_get_discover_places_filters_by_category()
    {
        $category1 = Category::factory()->create(['name' => 'Category 1']);
        $category2 = Category::factory()->create(['name' => 'Category 2']);

        $place1 = Place::factory()->create(['is_active' => true, 'name' => 'Place 1']);
        $place2 = Place::factory()->create(['is_active' => true, 'name' => 'Place 2']);

        $place1->categories()->attach($category1);
        $place2->categories()->attach($category2);

        $places = $this->repository->getDiscoverPlaces(['category_id' => $category1->id]);

        $this->assertCount(1, $places);
        $this->assertEquals('Place 1', $places->first()->name);
    }

    public function test_get_discover_places_includes_favorite_status_for_user()
    {
        $user = User::factory()->create();
        $place1 = Place::factory()->create(['is_active' => true]);
        $place2 = Place::factory()->create(['is_active' => true]);

        // User favorites place1
        $user->favoritePlaces()->attach($place1);

        $places = $this->repository->getDiscoverPlaces(['user_id' => $user->id]);

        $favoritedPlace = $places->where('id', $place1->id)->first();
        $nonFavoritedPlace = $places->where('id', $place2->id)->first();

        $this->assertTrue($favoritedPlace->is_favorited);
        $this->assertFalse($nonFavoritedPlace->is_favorited);
    }

    public function test_get_discover_places_sorts_featured_first()
    {
        $regularPlace = Place::factory()->create([
            'is_active' => true,
            'is_featured' => false,
            'priority' => 1,
            'name' => 'Regular Place'
        ]);

        $featuredPlace = Place::factory()->create([
            'is_active' => true,
            'is_featured' => true,
            'priority' => 1,
            'name' => 'Featured Place'
        ]);

        $places = $this->repository->getDiscoverPlaces();

        $this->assertEquals('Featured Place', $places->first()->name);
    }

    public function test_get_discover_places_includes_required_relationships()
    {
        $category = Category::factory()->create();
        $place = Place::factory()->create(['is_active' => true]);
        $place->categories()->attach($category);

        $places = $this->repository->getDiscoverPlaces();

        $this->assertTrue($places->first()->relationLoaded('categories'));
        $this->assertTrue($places->first()->relationLoaded('images'));
    }

    public function test_get_discover_places_selects_only_necessary_fields()
    {
        Place::factory()->create(['is_active' => true]);

        $places = $this->repository->getDiscoverPlaces();
        $place = $places->first();

        // Check that essential fields are present
        $this->assertNotNull($place->id);
        $this->assertNotNull($place->name);
        $this->assertNotNull($place->description);
        $this->assertNotNull($place->location);

        // Check that timestamps are not loaded (optimization)
        $this->assertArrayNotHasKey('created_at', $place->getAttributes());
        $this->assertArrayNotHasKey('updated_at', $place->getAttributes());
    }

    public function test_get_discover_places_correctly_identifies_user_favorites()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $place1 = Place::factory()->create(['is_active' => true, 'name' => 'Place 1']);
        $place2 = Place::factory()->create(['is_active' => true, 'name' => 'Place 2']);
        $place3 = Place::factory()->create(['is_active' => true, 'name' => 'Place 3']);

        // User1 favorites place1 and place2
        $user1->favoritePlaces()->attach([$place1->id, $place2->id]);

        // User2 favorites only place2
        $user2->favoritePlaces()->attach($place2->id);

        // Test for user1
        $placesForUser1 = $this->repository->getDiscoverPlaces(['user_id' => $user1->id]);

        $place1Result = $placesForUser1->where('id', $place1->id)->first();
        $place2Result = $placesForUser1->where('id', $place2->id)->first();
        $place3Result = $placesForUser1->where('id', $place3->id)->first();

        $this->assertTrue((bool) $place1Result->is_favorited, 'Place 1 should be favorited by user1');
        $this->assertTrue((bool) $place2Result->is_favorited, 'Place 2 should be favorited by user1');
        $this->assertFalse((bool) $place3Result->is_favorited, 'Place 3 should not be favorited by user1');

        // Test for user2
        $placesForUser2 = $this->repository->getDiscoverPlaces(['user_id' => $user2->id]);

        $place1ResultUser2 = $placesForUser2->where('id', $place1->id)->first();
        $place2ResultUser2 = $placesForUser2->where('id', $place2->id)->first();
        $place3ResultUser2 = $placesForUser2->where('id', $place3->id)->first();

        $this->assertFalse((bool) $place1ResultUser2->is_favorited, 'Place 1 should not be favorited by user2');
        $this->assertTrue((bool) $place2ResultUser2->is_favorited, 'Place 2 should be favorited by user2');
        $this->assertFalse((bool) $place3ResultUser2->is_favorited, 'Place 3 should not be favorited by user2');
    }
}
