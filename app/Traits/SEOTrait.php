<?php

namespace App\Traits;

use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Artesaos\SEOTools\Facades\JsonLd;

trait SEOTrait
{
    /**
     * Configure SEO for a page
     */
    protected function configureSEO(array $config): void
    {
        // Meta tags
        if (isset($config['title'])) {
            SEOMeta::setTitle($config['title']);
        }

        if (isset($config['description'])) {
            SEOMeta::setDescription($config['description']);
        }

        if (isset($config['keywords'])) {
            SEOMeta::setKeywords($config['keywords']);
        }

        if (isset($config['canonical'])) {
            SEOMeta::setCanonical($config['canonical']);
        }

        if (isset($config['robots'])) {
            SEOMeta::setRobots($config['robots']);
        }

        // Open Graph
        if (isset($config['og_title'])) {
            OpenGraph::setTitle($config['og_title']);
        }

        if (isset($config['og_description'])) {
            OpenGraph::setDescription($config['og_description']);
        }

        if (isset($config['og_type'])) {
            OpenGraph::setType($config['og_type']);
        }

        if (isset($config['og_url'])) {
            OpenGraph::setUrl($config['og_url']);
        }

        if (isset($config['og_image'])) {
            OpenGraph::addImage($config['og_image']);
        }

        if (isset($config['og_site_name'])) {
            OpenGraph::setSiteName($config['og_site_name']);
        }

        if (isset($config['og_locale'])) {
            OpenGraph::addProperty('locale', $config['og_locale']);
        }

        // Twitter Card
        if (isset($config['twitter_card'])) {
            TwitterCard::setType($config['twitter_card']);
        }

        if (isset($config['twitter_site'])) {
            TwitterCard::setSite($config['twitter_site']);
        }

        if (isset($config['twitter_title'])) {
            TwitterCard::setTitle($config['twitter_title']);
        }

        if (isset($config['twitter_description'])) {
            TwitterCard::setDescription($config['twitter_description']);
        }

        if (isset($config['twitter_image'])) {
            TwitterCard::setImage($config['twitter_image']);
        }

        // JSON-LD
        if (isset($config['jsonld'])) {
            JsonLd::setTitle($config['jsonld']['title'] ?? $config['title'] ?? '');
            JsonLd::setDescription($config['jsonld']['description'] ?? $config['description'] ?? '');
            JsonLd::setType($config['jsonld']['type'] ?? 'WebPage');

            if (isset($config['jsonld']['url'])) {
                JsonLd::setUrl($config['jsonld']['url']);
            }

            if (isset($config['jsonld']['image'])) {
                JsonLd::addImage($config['jsonld']['image']);
            }
        }
    }

    /**
     * Configure SEO for homepage
     */
    protected function configureHomepageSEO(): void
    {
        $this->configureSEO([
            'title' => 'MbokaTour - Redécouvre Kinshasa autrement',
            'description' => 'L\'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi.',
            'keywords' => ['Kinshasa', 'tourisme', 'Congo', 'RDC', 'voyage', 'culture', 'événements', 'lieux', 'découverte', 'guide', 'application mobile', 'bons plans'],
            'canonical' => url('/'),
            'robots' => 'index,follow',
            'og_title' => 'MbokaTour - Redécouvre Kinshasa autrement',
            'og_description' => 'L\'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi.',
            'og_type' => 'website',
            'og_url' => url('/'),
            'og_image' => asset('images/og-image.jpg'),
            'og_site_name' => 'MbokaTour',
            'og_locale' => 'fr_FR',
            'twitter_card' => 'summary_large_image',
            'twitter_site' => '@MbokaTour',
            'twitter_creator' => '@MbokaTour',
            'twitter_title' => 'MbokaTour - Redécouvre Kinshasa autrement',
            'twitter_description' => 'L\'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi.',
            'twitter_image' => asset('images/og-image.jpg'),
            'jsonld' => [
                'title' => 'MbokaTour - Redécouvre Kinshasa autrement',
                'description' => 'L\'application qui te fait redécouvrir Kinshasa autrement. Découvre les meilleurs endroits à visiter, les événements culturels et les bons plans près de chez toi.',
                'type' => 'WebApplication',
                'url' => url('/'),
                'image' => asset('images/og-image.jpg'),
            ],
        ]);
    }

    /**
     * Configure SEO for place pages
     */
    protected function configurePlaceSEO($place): void
    {
        $this->configureSEO([
            'title' => $place->name . ' - MbokaTour',
            'description' => $place->description ? substr(strip_tags($place->description), 0, 160) : 'Découvre ' . $place->name . ' à Kinshasa avec MbokaTour.',
            'keywords' => ['Kinshasa', $place->name, 'tourisme', 'Congo', 'RDC', 'voyage', 'lieux', 'découverte'],
            'canonical' => route('places.show', $place),
            'robots' => 'index,follow',
            'og_title' => $place->name . ' - MbokaTour',
            'og_description' => $place->description ? substr(strip_tags($place->description), 0, 160) : 'Découvre ' . $place->name . ' à Kinshasa avec MbokaTour.',
            'og_type' => 'place',
            'og_url' => route('places.show', $place),
            'og_image' => $place->images->first() ? asset('storage/' . $place->images->first()->path) : asset('images/og-image.jpg'),
            'og_site_name' => 'MbokaTour',
            'og_locale' => 'fr_FR',
            'twitter_card' => 'summary_large_image',
            'twitter_site' => '@MbokaTour',
            'twitter_creator' => '@MbokaTour',
            'twitter_title' => $place->name . ' - MbokaTour',
            'twitter_description' => $place->description ? substr(strip_tags($place->description), 0, 160) : 'Découvre ' . $place->name . ' à Kinshasa avec MbokaTour.',
            'twitter_image' => $place->images->first() ? asset('storage/' . $place->images->first()->path) : asset('images/og-image.jpg'),
            'jsonld' => [
                'title' => $place->name . ' - MbokaTour',
                'description' => $place->description ? substr(strip_tags($place->description), 0, 160) : 'Découvre ' . $place->name . ' à Kinshasa avec MbokaTour.',
                'type' => 'Place',
                'url' => route('places.show', $place),
                'image' => $place->images->first() ? asset('storage/' . $place->images->first()->path) : asset('images/og-image.jpg'),
            ],
        ]);
    }
}
