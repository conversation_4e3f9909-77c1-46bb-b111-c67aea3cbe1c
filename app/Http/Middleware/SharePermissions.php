<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Symfony\Component\HttpFoundation\Response;

class SharePermissions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (Auth::check()) {
            $user = Auth::user();
            
            Inertia::share([
                'auth.permissions' => [
                    'manage_places' => $user->can('manage-places'),
                    'manage_events' => $user->can('manage-events'),
                    'manage_categories' => $user->can('manage-categories'),
                    'manage_users' => $user->can('manage-users'),
                ],
                'auth.roles' => [
                    'is_admin' => $user->hasRole('admin'),
                ],
            ]);
        }

        return $next($request);
    }
}
