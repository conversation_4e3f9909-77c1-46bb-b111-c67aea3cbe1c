<?php

namespace App\Http\Controllers;

use App\Models\Category;
use App\Models\Comment;
use App\Models\Place;
use App\Models\PlaceLike;
use App\Models\User;
use App\Models\UserPlaceFavorite;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    public function index(): Response
    {
        // Statistiques générales
        $totalPlaces = Place::count();
        $totalUsers = User::count();
        $totalCategories = Category::count();
        $totalLikes = PlaceLike::count();
        $totalComments = Comment::count();
        $totalFavorites = UserPlaceFavorite::count();

        // Statistiques des places
        $activePlaces = Place::where('is_active', true)->count();
        $featuredPlaces = Place::where('is_featured', true)->count();
        $placesWithImages = Place::whereHas('images')->count();

        // Activité récente (7 derniers jours)
        $recentPlaces = Place::where('created_at', '>=', now()->subDays(7))->count();
        $recentUsers = User::where('created_at', '>=', now()->subDays(7))->count();
        $recentLikes = PlaceLike::where('created_at', '>=', now()->subDays(7))->count();
        $recentComments = Comment::where('created_at', '>=', now()->subDays(7))->count();

        // Top 10 des lieux les plus populaires (par vues)
        $popularPlaces = Place::with(['categories', 'images'])
            ->orderBy('views_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($place) {
                return [
                    'id' => $place->id,
                    'name' => $place->name,
                    'views_count' => $place->views_count,
                    'likes_count' => $place->likes()->count(),
                    'comments_count' => $place->comments()->count(),
                    'categories' => $place->categories->pluck('name'),
                    'main_image_url' => $place->main_image_url,
                ];
            });

        // Top 10 des lieux les plus likés
        $mostLikedPlaces = Place::with(['categories'])
            ->withCount(['likes', 'comments'])
            ->orderBy('likes_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($place) {
                return [
                    'id' => $place->id,
                    'name' => $place->name,
                    'likes_count' => $place->likes_count,
                    'views_count' => $place->views_count,
                    'comments_count' => $place->comments_count,
                    'categories' => $place->categories->pluck('name'),
                    'main_image_url' => $place->main_image_url,
                ];
            });

        // Statistiques par catégorie (uniquement celles avec des places)
        $categoriesStats = Category::whereHas('places')
            ->withCount('places')
            ->orderBy('places_count', 'desc')
            ->get()
            ->map(function ($category) {
                return [
                    'name' => $category->name,
                    'places_count' => $category->places_count,
                    'icon' => $category->icon,
                    'color' => $category->color,
                ];
            });

        // Activité des 30 derniers jours (pour graphique)
        $dailyActivity = collect(range(29, 0))->map(function ($daysAgo) {
            $date = now()->subDays($daysAgo);
            return [
                'date' => $date->format('Y-m-d'),
                'places' => Place::whereDate('created_at', $date)->count(),
                'users' => User::whereDate('created_at', $date)->count(),
                'likes' => PlaceLike::whereDate('created_at', $date)->count(),
                'comments' => Comment::whereDate('created_at', $date)->count(),
            ];
        });

        // Utilisateurs les plus actifs (par likes donnés)
        $activeUsers = User::withCount('placeLikes')
            ->orderBy('place_likes_count', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'likes_count' => $user->place_likes_count,
                    'favorites_count' => $user->favoritePlaces()->count(),
                    'comments_count' => $user->comments()->count(),
                ];
            });

        // Commentaires récents
        $recentCommentsData = Comment::with(['user:id,name', 'place:id,name'])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($comment) {
                return [
                    'id' => $comment->id,
                    'content' => substr($comment->content, 0, 100) . (strlen($comment->content) > 100 ? '...' : ''),
                    'rating' => $comment->rating,
                    'user_name' => $comment->user->name,
                    'place_name' => $comment->place->name,
                    'created_at' => $comment->created_at->diffForHumans(),
                ];
            });

        return Inertia::render('Dashboard', [
            'stats' => [
                'total' => [
                    'places' => $totalPlaces,
                    'users' => $totalUsers,
                    'categories' => $totalCategories,
                    'likes' => $totalLikes,
                    'comments' => $totalComments,
                    'favorites' => $totalFavorites,
                ],
                'places' => [
                    'active' => $activePlaces,
                    'featured' => $featuredPlaces,
                    'with_images' => $placesWithImages,
                ],
                'recent' => [
                    'places' => $recentPlaces,
                    'users' => $recentUsers,
                    'likes' => $recentLikes,
                    'comments' => $recentComments,
                ],
            ],
            'popularPlaces' => $popularPlaces,
            'mostLikedPlaces' => $mostLikedPlaces,
            'categoriesStats' => $categoriesStats,
            'dailyActivity' => $dailyActivity,
            'activeUsers' => $activeUsers,
            'recentComments' => $recentCommentsData,
        ]);
    }
}
