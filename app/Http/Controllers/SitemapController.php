<?php

namespace App\Http\Controllers;

use App\Models\Place;
use App\Models\Category;
use Illuminate\Http\Response;

class SitemapController extends Controller
{
    /**
     * Generate the sitemap.xml
     */
    public function index(): Response
    {
        $places = Place::where('is_active', true)
            ->select('id', 'slug', 'updated_at')
            ->get();

        $categories = Category::select('id', 'slug', 'updated_at')->get();

        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";

        // Homepage
        $sitemap .= $this->addUrl(url('/'), now(), 'daily', '1.0');

        // Places (when routes are available)
        // foreach ($places as $place) {
        //     $url = route('places.show', $place->slug ?? $place->id);
        //     $sitemap .= $this->addUrl($url, $place->updated_at, 'weekly', '0.8');
        // }

        // Categories (when routes are available)
        // foreach ($categories as $category) {
        //     $url = route('categories.show', $category->slug ?? $category->id);
        //     $sitemap .= $this->addUrl($url, $category->updated_at, 'weekly', '0.6');
        // }

        // Static pages (when routes are available)
        // $staticPages = [
        //     ['url' => route('places.index'), 'priority' => '0.9'],
        //     ['url' => route('categories.index'), 'priority' => '0.7'],
        //     ['url' => route('events.index'), 'priority' => '0.7'],
        // ];

        // foreach ($staticPages as $page) {
        //     $sitemap .= $this->addUrl($page['url'], now(), 'weekly', $page['priority']);
        // }

        $sitemap .= '</urlset>';

        return response($sitemap, 200, [
            'Content-Type' => 'application/xml',
            'Cache-Control' => 'public, max-age=3600', // Cache for 1 hour
        ]);
    }

    /**
     * Add a URL to the sitemap
     */
    private function addUrl(string $url, $lastmod, string $changefreq = 'weekly', string $priority = '0.5'): string
    {
        $xml = "  <url>\n";
        $xml .= "    <loc>" . htmlspecialchars($url) . "</loc>\n";
        $xml .= "    <lastmod>" . $lastmod->format('Y-m-d\TH:i:s+00:00') . "</lastmod>\n";
        $xml .= "    <changefreq>" . $changefreq . "</changefreq>\n";
        $xml .= "    <priority>" . $priority . "</priority>\n";
        $xml .= "  </url>\n";

        return $xml;
    }
}
