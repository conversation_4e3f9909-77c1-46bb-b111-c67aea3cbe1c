<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StoreUserRequest;
use App\Http\Requests\Admin\UpdateUserRequest;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    protected $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function index(): Response
    {
        return Inertia::render('Admin/Users/<USER>', [
            'users' => $this->userService->getAllUsers(),
        ]);
    }

    public function create(): Response
    {
        return Inertia::render('Admin/Users/<USER>', [
            'roles' => $this->userService->getAllRoles(),
        ]);
    }

    public function store(StoreUserRequest $request): RedirectResponse
    {
        $this->userService->createUser($request->validated());

        return redirect()->route('admin.users.index');
    }

    public function show(User $user): Response
    {
        return Inertia::render('Admin/Users/<USER>', [
            'user' => $this->userService->getUserById($user->id),
        ]);
    }

    public function edit(User $user): Response
    {
        return Inertia::render('Admin/Users/<USER>', [
            'user' => $this->userService->getUserById($user->id),
            'roles' => $this->userService->getAllRoles(),
        ]);
    }

    public function update(UpdateUserRequest $request, User $user): RedirectResponse
    {
        $this->userService->updateUser($request->validated(), $user);

        return redirect()->route('admin.users.index');
    }

    public function destroy(User $user): RedirectResponse
    {
        $this->userService->deleteUser($user);

        return redirect()->route('admin.users.index');
    }
}
