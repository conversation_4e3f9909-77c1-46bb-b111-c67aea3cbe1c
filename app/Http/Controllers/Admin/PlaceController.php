<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\StorePlaceRequest;
use App\Http\Requests\Admin\UpdatePlaceRequest;
use App\Models\Category;
use App\Models\Place;
use App\Models\PlaceImage;
use App\Services\PlaceService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class PlaceController extends Controller
{
    protected $placeService;

    public function __construct(PlaceService $placeService)
    {
        $this->placeService = $placeService;
    }

    public function index()
    {
        return Inertia::render('Admin/Places/Index', [
            'places' => $this->placeService->getAllPlaces(),
        ]);
    }

    public function create()
    {
        return Inertia::render('Admin/Places/Create', [
            'categories' => Category::all(),
        ]);
    }

    public function store(StorePlaceRequest $request)
    {
        $this->placeService->createPlace($request->validated());
        return redirect()->route('admin.places.index');
    }

    public function show(Place $place)
    {
        return Inertia::render('Admin/Places/Show', [
            'place' => $this->placeService->getPlaceById($place->id),
        ]);
    }

    public function edit(Place $place)
    {
        return Inertia::render('Admin/Places/Edit', [
            'place' => $this->placeService->getPlaceById($place->id),
            'categories' => Category::all(),
        ]);
    }

    public function update(UpdatePlaceRequest $request, Place $place)
    {
        Log::info('PlaceController::update called', [
            'place_id' => $place->id,
            'validated_data_keys' => array_keys($request->validated())
        ]);

        try {
            $this->placeService->updatePlace($place->id, $request->validated());
            Log::info('Place updated successfully', ['place_id' => $place->id]);
            return redirect()->route('admin.places.index');
        } catch (\Exception $e) {
            Log::error('Error updating place', [
                'place_id' => $place->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Update place with images using POST method
     */
    public function updateWithImages(UpdatePlaceRequest $request, Place $place)
    {
        Log::info('PlaceController::updateWithImages called', [
            'place_id' => $place->id,
            'validated_data_keys' => array_keys($request->validated()),
            'has_main_image_in_data' => isset($request->validated()['main_image_url']),
            'has_images_in_data' => isset($request->validated()['images'])
        ]);

        try {
            $this->placeService->updatePlace($place->id, $request->validated());
            Log::info('Place updated successfully with images', ['place_id' => $place->id]);
            return redirect()->route('admin.places.index');
        } catch (\Exception $e) {
            Log::error('Error updating place with images', [
                'place_id' => $place->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function destroy(Place $place)
    {
        $this->placeService->deletePlace($place->id);
        return redirect()->route('admin.places.index');
    }

    /**
     * Delete a specific image from a place
     */
    public function deleteImage(Place $place, PlaceImage $image)
    {
        // Vérifier que l'image appartient bien à cette place
        if ($image->place_id !== $place->id) {
            return response()->json(['error' => 'Image not found for this place'], 404);
        }

        // Supprimer le fichier du stockage
        $imagePath = $image->getRawImagePath();
        if ($imagePath && Storage::disk('public')->exists($imagePath)) {
            Storage::disk('public')->delete($imagePath);
        }

        // Supprimer l'enregistrement de la base de données
        $image->delete();

        return response()->json(['success' => 'Image deleted successfully']);
    }
}
