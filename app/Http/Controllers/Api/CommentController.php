<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Models\Place;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class CommentController extends Controller
{
    /**
     * Display comments for a specific place.
     */
    public function index(string $placeId): JsonResponse
    {
        $place = Place::findOrFail($placeId);
        
        $comments = $place->comments()
            ->with('user:id,name')
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($comment) {
                return [
                    'id' => $comment->id,
                    'content' => $comment->content,
                    'rating' => $comment->rating,
                    'created_at' => $comment->created_at,
                    'user' => [
                        'id' => $comment->user->id,
                        'name' => $comment->user->name,
                    ],
                ];
            });

        return response()->json($comments);
    }

    /**
     * Store a new comment for a place.
     */
    public function store(Request $request, string $placeId): JsonResponse
    {
        $place = Place::findOrFail($placeId);
        
        $validated = $request->validate([
            'content' => 'required|string|min:3|max:1000',
            'rating' => 'nullable|integer|min:1|max:5',
        ]);

        // Check if user already commented on this place
        $existingComment = Comment::where('place_id', $placeId)
            ->where('user_id', Auth::id())
            ->first();

        if ($existingComment) {
            throw ValidationException::withMessages([
                'comment' => ['Vous avez déjà commenté ce lieu.']
            ]);
        }

        $comment = Comment::create([
            'place_id' => $placeId,
            'user_id' => Auth::id(),
            'content' => $validated['content'],
            'rating' => $validated['rating'] ?? null,
            'is_approved' => true, // Auto-approve for now
        ]);

        // Load the user relationship for the response
        $comment->load('user:id,name');

        return response()->json([
            'message' => 'Commentaire ajouté avec succès.',
            'comment' => [
                'id' => $comment->id,
                'content' => $comment->content,
                'rating' => $comment->rating,
                'created_at' => $comment->created_at,
                'user' => [
                    'id' => $comment->user->id,
                    'name' => $comment->user->name,
                ],
            ],
        ], 201);
    }

    /**
     * Update a comment (only by the comment author).
     */
    public function update(Request $request, string $placeId, string $commentId): JsonResponse
    {
        $comment = Comment::where('id', $commentId)
            ->where('place_id', $placeId)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        $validated = $request->validate([
            'content' => 'required|string|min:3|max:1000',
            'rating' => 'nullable|integer|min:1|max:5',
        ]);

        $comment->update([
            'content' => $validated['content'],
            'rating' => $validated['rating'] ?? $comment->rating,
        ]);

        $comment->load('user:id,name');

        return response()->json([
            'message' => 'Commentaire mis à jour avec succès.',
            'comment' => [
                'id' => $comment->id,
                'content' => $comment->content,
                'rating' => $comment->rating,
                'created_at' => $comment->created_at,
                'updated_at' => $comment->updated_at,
                'user' => [
                    'id' => $comment->user->id,
                    'name' => $comment->user->name,
                ],
            ],
        ]);
    }

    /**
     * Delete a comment (only by the comment author).
     */
    public function destroy(string $placeId, string $commentId): JsonResponse
    {
        $comment = Comment::where('id', $commentId)
            ->where('place_id', $placeId)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        $comment->delete();

        return response()->json([
            'message' => 'Commentaire supprimé avec succès.',
        ]);
    }
}
