<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Place;
use App\Models\PlaceLike;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class PlaceLikeController extends Controller
{
    /**
     * Liker ou unliker un lieu
     */
    public function toggle(Request $request): JsonResponse
    {
        $request->validate([
            'place_id' => 'required|exists:places,id'
        ]);

        $user = Auth::user();
        $place = Place::findOrFail($request->place_id);

        // Vérifier si l'utilisateur a déjà liké ce lieu
        $existingLike = PlaceLike::where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->first();

        if ($existingLike) {
            // Unliker - supprimer le like
            $existingLike->delete();
            $isLiked = false;
            $message = 'Like retiré avec succès.';
        } else {
            // Liker - créer un nouveau like
            PlaceLike::create([
                'user_id' => $user->id,
                'place_id' => $place->id,
            ]);
            $isLiked = true;
            $message = 'Lieu liké avec succès.';
        }

        // Compter le nombre total de likes pour ce lieu
        $likesCount = PlaceLike::where('place_id', $place->id)->count();

        return response()->json([
            'message' => $message,
            'is_liked' => $isLiked,
            'likes_count' => $likesCount,
        ]);
    }

    /**
     * Obtenir le statut de like d'un lieu pour l'utilisateur connecté
     */
    public function status(string $placeId): JsonResponse
    {
        $user = Auth::user();
        $place = Place::findOrFail($placeId);

        $isLiked = PlaceLike::where('user_id', $user->id)
            ->where('place_id', $place->id)
            ->exists();

        $likesCount = PlaceLike::where('place_id', $place->id)->count();

        return response()->json([
            'is_liked' => $isLiked,
            'likes_count' => $likesCount,
        ]);
    }

    /**
     * Obtenir la liste des lieux likés par l'utilisateur
     */
    public function index(): JsonResponse
    {
        $user = Auth::user();
        
        $likedPlaces = Place::whereHas('likes', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->with(['categories', 'images'])
        ->orderBy('created_at', 'desc')
        ->get();

        return response()->json($likedPlaces);
    }

    /**
     * Obtenir les statistiques de likes d'un lieu
     */
    public function stats(string $placeId): JsonResponse
    {
        $place = Place::findOrFail($placeId);
        
        $likesCount = PlaceLike::where('place_id', $place->id)->count();
        
        // Obtenir les utilisateurs qui ont liké (limité aux 10 derniers)
        $recentLikes = PlaceLike::where('place_id', $place->id)
            ->with('user:id,name')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return response()->json([
            'likes_count' => $likesCount,
            'recent_likes' => $recentLikes->map(function ($like) {
                return [
                    'user' => $like->user,
                    'liked_at' => $like->created_at,
                ];
            }),
        ]);
    }
}
