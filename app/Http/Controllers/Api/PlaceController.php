<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PlaceService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PlaceController extends Controller
{
    protected $placeService;

    public function __construct(PlaceService $placeService)
    {
        $this->placeService = $placeService;
    }

    /**
     * Display a listing of the resource.
     */
    public function discover(Request $request)
    {
        $options = [];

        // Get limit from request (default: 50, max: 100)
        if ($request->has('limit')) {
            $options['limit'] = min((int) $request->get('limit', 50), 100);
        }

        // Get category filter
        if ($request->has('category_id')) {
            $options['category_id'] = $request->get('category_id');
        }

        // Get user ID for favorites and likes
        if (Auth::check()) {
            $options['user_id'] = Auth::id();
        }

        $places = $this->placeService->getDiscoverPlaces($options);
        return response()->json($places);
    }

    /**
     * Display the specified resource and increment views count.
     */
    public function show(string $id)
    {
        $userId = Auth::check() ? Auth::id() : null;
        $place = $this->placeService->getPlaceByIdAndIncrementViews($id, $userId);
        return response()->json($place);
    }

    public function search(Request $request)
    {
        $query = $request->input('query');
        $places = $this->placeService->searchPlaces($query);
        return response()->json($places);
    }

    /**
     * Get nearby places based on user's location
     */
    public function nearby(Request $request)
    {
        // Validate required parameters
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:100'
        ]);

        $latitude = $request->input('latitude');
        $longitude = $request->input('longitude');
        $radius = $request->input('radius', 20.0); // Default 20km

        // Get user ID for favorites and likes if authenticated
        $userId = Auth::check() ? Auth::id() : null;

        try {
            $places = $this->placeService->getNearbyPlaces($latitude, $longitude, $radius, $userId);
            return response()->json($places);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Une erreur est survenue lors de la recherche des lieux à proximité.',
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
