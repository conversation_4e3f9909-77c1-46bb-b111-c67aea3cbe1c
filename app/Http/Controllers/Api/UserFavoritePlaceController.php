<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Place;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class UserFavoritePlaceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = Auth::user();
        return response()->json($user->favoritePlaces);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $place = Place::findOrFail($request->place_id);
        $user->favoritePlaces()->syncWithoutDetaching($place);

        return response()->json(['message' => 'Place added to favorites.']);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $place_id)
    {
        $user = Auth::user();
        $place = Place::findOrFail($place_id);
        $user->favoritePlaces()->detach($place);

        return response()->json(['message' => 'Place removed from favorites.']);
    }
}
