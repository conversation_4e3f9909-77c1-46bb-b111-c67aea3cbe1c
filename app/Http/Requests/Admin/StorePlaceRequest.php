<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class StorePlaceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'location' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'neighborhood' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:255',
            'latitude' => 'nullable|numeric',
            'longitude' => 'nullable|numeric',
            'price' => 'nullable|numeric',
            'opening_hours' => 'nullable|string',
            'is_free' => 'nullable|boolean',
            'status' => 'required|string|in:active,inactive',
            'main_image_url' => 'nullable|file|mimes:jpeg,png,jpg,gif,svg,mp4,mov,ogg,qt',
            'mediaType' => 'nullable|string|in:image,video',
            'images' => 'nullable|array',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif,svg',
            'categories' => 'nullable|array',
            'categories.*' => 'exists:categories,id',
        ];
    }
}