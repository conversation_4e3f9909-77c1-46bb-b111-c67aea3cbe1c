<?php

namespace App\Repositories\Interfaces;

use Illuminate\Database\Eloquent\Collection;
use App\Models\Category;

interface CategoryRepositoryInterface
{
    public function all(): Collection;
    public function allWithPlaces(): Collection;
    public function create(array $data): Category;
    public function update(array $data, Category $category): bool;
    public function delete(Category $category): bool;
    public function find(int $id): ?Category;
}