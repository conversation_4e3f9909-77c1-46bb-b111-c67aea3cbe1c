<?php

namespace App\Repositories\Interfaces;

interface PlaceRepositoryInterface
{
    public function getDiscoverPlaces(array $options = []);
    public function findById(string $id, $userId = null);
    public function search(string $query);
    public function getNearbyPlaces(float $latitude, float $longitude, float $radius, $userId = null);
    public function getAll();
    public function create(array $data);
    public function update(string $id, array $data);
    public function delete(string $id);
    public function incrementViews(string $id);
}