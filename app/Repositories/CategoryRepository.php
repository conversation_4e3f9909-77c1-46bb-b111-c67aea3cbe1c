<?php

namespace App\Repositories;

use App\Models\Category;
use App\Repositories\Interfaces\CategoryRepositoryInterface;
use Illuminate\Database\Eloquent\Collection;

class CategoryRepository implements CategoryRepositoryInterface
{
    public function all(): Collection
    {
        return Category::orderBy('display_order')->get();
    }

    public function allWithPlaces(): Collection
    {
        return Category::whereHas('places', function ($query) {
            $query->where('is_active', true);
        })->orderBy('display_order')->get();
    }

    public function create(array $data): Category
    {
        return Category::create($data);
    }

    public function update(array $data, Category $category): bool
    {
        return $category->update($data);
    }

    public function delete(Category $category): bool
    {
        return $category->delete();
    }

    public function find(int $id): ?Category
    {
        return Category::find($id);
    }
}