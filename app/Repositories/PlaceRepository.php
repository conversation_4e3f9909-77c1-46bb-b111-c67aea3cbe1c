<?php

namespace App\Repositories;

use App\Models\Place;
use App\Repositories\Interfaces\PlaceRepositoryInterface;
use Illuminate\Support\Facades\DB;

class PlaceRepository implements PlaceRepositoryInterface
{
    public function getDiscoverPlaces(array $options = [])
    {
        $limit = $options['limit'] ?? 20;
        $categoryId = $options['category_id'] ?? null;
        $userId = $options['user_id'] ?? null;

        $query = Place::where('is_active', true)
            ->select([
                'id', 'name', 'description', 'location', 'price', 'is_free',
                'latitude', 'longitude', 'address', 'neighborhood', 'city',
                'main_image_url', 'mediaType', 'is_featured', 'views_count', 'priority'
            ])
            ->with([
                'categories:id,name,icon,color',
                'images:id,place_id,image_url'
            ]);

        // Filter by category if specified
        if ($categoryId) {
            $query->whereHas('categories', function ($q) use ($categoryId) {
                $q->where('categories.id', $categoryId);
            });
        }

        // Add user favorites and likes status if user is specified
        if ($userId) {
            $query->addSelect([
                'is_favorited' => function ($subQuery) use ($userId) {
                    $subQuery->selectRaw('CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END')
                        ->from('user_place_favorites')
                        ->whereColumn('user_place_favorites.place_id', 'places.id')
                        ->where('user_place_favorites.user_id', $userId);
                },
                'is_liked' => function ($subQuery) use ($userId) {
                    $subQuery->selectRaw('CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END')
                        ->from('place_likes')
                        ->whereColumn('place_likes.place_id', 'places.id')
                        ->where('place_likes.user_id', $userId);
                }
            ]);
        }

        // Add likes count for all places
        $query->addSelect([
            'likes_count' => function ($subQuery) {
                $subQuery->selectRaw('COUNT(*)')
                    ->from('place_likes')
                    ->whereColumn('place_likes.place_id', 'places.id');
            }
        ]);

        // Improved sorting logic: featured first, then by priority, then random
        $places = $query->orderByDesc('is_featured')
            ->orderByDesc('priority')
            ->orderByDesc('views_count')
            ->inRandomOrder()
            ->limit($limit)
            ->get();

        // Ensure is_favorited and is_liked are boolean for all places
        $places->each(function ($place) use ($userId) {
            if ($userId) {
                $place->is_favorited = (bool) $place->is_favorited;
                $place->is_liked = (bool) $place->is_liked;
            } else {
                $place->is_favorited = false;
                $place->is_liked = false;
            }
        });

        return $places;
    }

    public function findById(string $id, $userId = null)
    {
        $query = Place::with([
            'images',
            'categories',
            'comments' => function ($query) {
                $query->with('user:id,name')
                      ->where('is_approved', true)
                      ->orderBy('created_at', 'desc')
                      ->limit(10); // Limiter à 10 commentaires récents
            }
        ]);

        // Add likes count
        $query->addSelect([
            '*',
            'likes_count' => function ($subQuery) {
                $subQuery->selectRaw('COUNT(*)')
                    ->from('place_likes')
                    ->whereColumn('place_likes.place_id', 'places.id');
            }
        ]);

        // Add user-specific data if user is provided
        if ($userId) {
            $query->addSelect([
                'is_favorited' => function ($subQuery) use ($userId) {
                    $subQuery->selectRaw('CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END')
                        ->from('user_place_favorites')
                        ->whereColumn('user_place_favorites.place_id', 'places.id')
                        ->where('user_place_favorites.user_id', $userId);
                },
                'is_liked' => function ($subQuery) use ($userId) {
                    $subQuery->selectRaw('CASE WHEN COUNT(*) > 0 THEN 1 ELSE 0 END')
                        ->from('place_likes')
                        ->whereColumn('place_likes.place_id', 'places.id')
                        ->where('place_likes.user_id', $userId);
                }
            ]);
        }

        $place = $query->findOrFail($id);

        // Set default values for non-authenticated users
        if (!$userId) {
            $place->is_favorited = false;
            $place->is_liked = false;
        } else {
            $place->is_favorited = (bool) $place->is_favorited;
            $place->is_liked = (bool) $place->is_liked;
        }

        return $place;
    }

    public function incrementViews(string $id)
    {
        return Place::where('id', $id)->increment('views_count');
    }

    public function search(string $query)
    {
        return Place::where('is_active', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%")
                  ->orWhere('neighborhood', 'like', "%{$query}%");
            })
            ->get();
    }

    /**
     * Get nearby places using Haversine formula
     */
    public function getNearbyPlaces(float $latitude, float $longitude, float $radius, $userId = null)
    {
        // Get all active places with coordinates and calculate distance
        $places = Place::where('is_active', true)
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->select([
                'id', 'name', 'description', 'latitude', 'longitude', 'main_image_url',
                'mediaType', 'address', 'neighborhood', 'city', 'price',
                'is_free', 'opening_hours', 'status', 'is_active', 'is_featured',
                'views_count', 'priority'
            ])
            ->with([
                'categories:id,name',
                'images:id,place_id,image_url'
            ])
            ->get();

        // Filter places by distance using PHP calculation
        $nearbyPlaces = $places->filter(function ($place) use ($latitude, $longitude, $radius) {
            $distance = $this->calculateDistance($latitude, $longitude, $place->latitude, $place->longitude);
            $place->setAttribute('distance', round($distance, 2));
            return $distance <= $radius;
        });

        // Sort by distance
        $nearbyPlaces = $nearbyPlaces->sortBy('distance');

        // Add user-specific data if user is provided
        if ($userId) {
            $placeIds = $nearbyPlaces->pluck('id')->toArray();

            if (!empty($placeIds)) {
                // Get favorites
                $favorites = DB::table('user_place_favorites')
                    ->where('user_id', $userId)
                    ->whereIn('place_id', $placeIds)
                    ->pluck('place_id')
                    ->toArray();

                // Get likes
                $likes = DB::table('place_likes')
                    ->where('user_id', $userId)
                    ->whereIn('place_id', $placeIds)
                    ->pluck('place_id')
                    ->toArray();

                // Set user-specific flags
                $nearbyPlaces->each(function ($place) use ($favorites, $likes) {
                    $place->setAttribute('is_favorited', in_array($place->id, $favorites));
                    $place->setAttribute('is_liked', in_array($place->id, $likes));
                });
            }
        } else {
            $nearbyPlaces->each(function ($place) {
                $place->setAttribute('is_favorited', false);
                $place->setAttribute('is_liked', false);
            });
        }

        // Add likes count for all places
        $placeIds = $nearbyPlaces->pluck('id')->toArray();
        if (!empty($placeIds)) {
            $likesCounts = DB::table('place_likes')
                ->whereIn('place_id', $placeIds)
                ->groupBy('place_id')
                ->selectRaw('place_id, COUNT(*) as count')
                ->pluck('count', 'place_id')
                ->toArray();

            $nearbyPlaces->each(function ($place) use ($likesCounts) {
                $place->setAttribute('likes_count', $likesCounts[$place->id] ?? 0);
                $place->setAttribute('media_type', $place->mediaType); // Ensure media_type is available
            });
        }

        return $nearbyPlaces->values();
    }

    /**
     * Calculate distance between two points using Haversine formula
     */
    private function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    public function getAll()
    {
        return Place::with('categories')->latest()->get();
    }

    public function create(array $data)
    {
        $place = Place::create($data);
        if (isset($data['categories'])) {
            $place->categories()->sync($data['categories']);
        }
        return $place;
    }

    public function update(string $id, array $data)
    {
        $place = Place::findOrFail($id);
        $place->update($data);
        if (isset($data['categories'])) {
            $place->categories()->sync($data['categories']);
        }
        return $place;
    }

    public function delete(string $id)
    {
        Place::findOrFail($id)->delete();
    }
}