<?php

namespace App\Repositories;

use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;

class UserRepository implements UserRepositoryInterface
{
    public function getAll()
    {
        return User::all();
    }

    public function getAllWithRoles()
    {
        return User::with('roles')->orderBy('created_at', 'desc')->get();
    }

    public function find(int $id)
    {
        return User::findOrFail($id);
    }

    public function findWithRoles(int $id)
    {
        return User::with('roles')->findOrFail($id);
    }

    public function create(array $data)
    {
        return User::create($data);
    }

    public function update(int $id, array $data)
    {
        $user = $this->find($id);
        $user->update($data);
        return $user->fresh();
    }

    public function delete(int $id)
    {
        $user = $this->find($id);
        return $user->delete();
    }
}
