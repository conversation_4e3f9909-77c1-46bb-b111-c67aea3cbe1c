<?php

namespace App\Services;

use App\Repositories\Interfaces\PlaceRepositoryInterface;
use App\Services\CloudinaryService;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Exception;

class PlaceService
{
    protected $placeRepository;
    protected $cloudinaryService;

    public function __construct(PlaceRepositoryInterface $placeRepository, CloudinaryService $cloudinaryService)
    {
        $this->placeRepository = $placeRepository;
        $this->cloudinaryService = $cloudinaryService;
    }

    public function getDiscoverPlaces(array $options = [])
    {
        return $this->placeRepository->getDiscoverPlaces($options);
    }

    public function getPlaceById(string $id, $userId = null)
    {
        return $this->placeRepository->findById($id, $userId);
    }

    public function getPlaceByIdAndIncrementViews(string $id, $userId = null)
    {
        // Increment views count
        $this->placeRepository->incrementViews($id);

        // Return the place with updated data
        return $this->placeRepository->findById($id, $userId);
    }

    public function searchPlaces(string $query)
    {
        return $this->placeRepository->search($query);
    }

    /**
     * Get nearby places based on coordinates and radius
     */
    public function getNearbyPlaces(float $latitude, float $longitude, float $radius, $userId = null)
    {
        return $this->placeRepository->getNearbyPlaces($latitude, $longitude, $radius, $userId);
    }

    public function getAllPlaces()
    {
        return $this->placeRepository->getAll();
    }

    public function createPlace(array $data)
    {
        // Handle main image upload
        if (isset($data['main_image_url']) && $data['main_image_url'] instanceof UploadedFile) {
            $uploadResult = $this->cloudinaryService->uploadFile(
                $data['main_image_url'],
                'mbokatour/places/main'
            );
            $data['main_image_url'] = $uploadResult['secure_url'];

            // Auto-detect mediaType if not set
            if (empty($data['mediaType'])) {
                $data['mediaType'] = $uploadResult['resource_type'];
            }
        }

        // Extract images for separate handling
        $images = $data['images'] ?? [];
        unset($data['images']);

        $place = $this->placeRepository->create($data);

        // Handle multiple images upload
        if (!empty($images)) {
            $this->uploadPlaceImages($place, $images);
        }

        return $place;
    }

    public function updatePlace(string $id, array $data)
    {
        // Handle main image upload ONLY if a new file is provided
        if (isset($data['main_image_url'])) {
            if (!is_null($data['main_image_url'])) {
                if ($data['main_image_url'] instanceof UploadedFile) {
                    // Get the current place to potentially delete old image
                    $currentPlace = $this->placeRepository->findById($id);

                    // Delete old main image if it exists and is from Cloudinary
                    if ($currentPlace && $currentPlace->getRawMainImagePath()) {
                        $oldImageUrl = $currentPlace->getRawMainImagePath();
                        $this->deleteCloudinaryImage($oldImageUrl);
                    }

                    // Upload new image to Cloudinary
                    $uploadResult = $this->cloudinaryService->uploadFile(
                        $data['main_image_url'],
                        'mbokatour/places/main'
                    );
                    $data['main_image_url'] = $uploadResult['secure_url'];

                    // Auto-detect mediaType if not set
                    if (empty($data['mediaType'])) {
                        $data['mediaType'] = $uploadResult['resource_type'];
                    }
                }
            } else {
                // If main_image_url is explicitly null, it means delete the current image
                $currentPlace = $this->placeRepository->findById($id);
                if ($currentPlace && $currentPlace->getRawMainImagePath()) {
                    $oldImageUrl = $currentPlace->getRawMainImagePath();
                    $this->deleteCloudinaryImage($oldImageUrl);
                }
                $data['main_image_url'] = null;
                $data['mediaType'] = '';
            }
        }
        // If main_image_url is not in $data at all, we preserve the existing image

        // Extract images for separate handling
        $images = $data['images'] ?? [];
        unset($data['images']);

        $place = $this->placeRepository->update($id, $data);

        // Handle multiple images upload
        if (!empty($images)) {
            $this->uploadPlaceImages($place, $images);
        }

        return $place;
    }

    public function deletePlace(string $id)
    {
        // Récupérer la place avec ses images avant suppression
        $place = $this->placeRepository->findById($id);

        if (!$place) {
            throw new Exception("Place not found with ID: {$id}");
        }

        // Supprimer l'image principale si elle existe
        $this->deleteMainImageFile($place);

        // Supprimer toutes les images associées
        $this->deletePlaceImages($place);

        // Supprimer la place de la base de données
        return $this->placeRepository->delete($id);
    }

    /**
     * Upload multiple images for a place
     */
    private function uploadPlaceImages($place, array $images): void
    {
        foreach ($images as $image) {
            if ($image instanceof UploadedFile) {
                $uploadResult = $this->cloudinaryService->uploadFile(
                    $image,
                    'mbokatour/places/images'
                );
                $place->images()->create([
                    'image_url' => $uploadResult['secure_url'],
                ]);
            }
        }
    }

    /**
     * Delete main image file from Cloudinary
     */
    private function deleteMainImageFile($place): void
    {
        $mainImageUrl = $place->getRawMainImagePath();
        if ($mainImageUrl) {
            $this->deleteCloudinaryImage($mainImageUrl);
        }
    }

    /**
     * Delete all place images from Cloudinary and database
     */
    private function deletePlaceImages($place): void
    {
        $images = $place->images;

        foreach ($images as $image) {
            // Supprimer le fichier de Cloudinary
            $imageUrl = $image->getRawImagePath();
            if ($imageUrl) {
                $this->deleteCloudinaryImage($imageUrl);
            }
        }

        // Les enregistrements en base seront supprimés automatiquement
        // grâce à la contrainte onDelete('cascade') dans la migration
    }

    /**
     * Delete an image from Cloudinary
     */
    private function deleteCloudinaryImage(string $imageUrl): void
    {
        try {
            $publicId = $this->cloudinaryService->extractPublicId($imageUrl);
            $resourceType = $this->cloudinaryService->extractResourceType($imageUrl);

            if ($publicId) {
                $success = $this->cloudinaryService->deleteFile($publicId, $resourceType);
                if ($success) {
                    Log::info("Deleted Cloudinary image: {$publicId}");
                } else {
                    Log::warning("Failed to delete Cloudinary image: {$publicId}");
                }
            }
        } catch (Exception $e) {
            Log::error("Error deleting Cloudinary image: {$imageUrl}", [
                'error' => $e->getMessage()
            ]);
        }
    }

}