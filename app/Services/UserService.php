<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\Interfaces\UserRepositoryInterface;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class UserService
{
    protected $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    public function getAllUsers()
    {
        return $this->userRepository->getAllWithRoles();
    }

    public function getUserById(int $id)
    {
        return $this->userRepository->findWithRoles($id);
    }

    public function createUser(array $data)
    {
        // Hash du mot de passe
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        // Créer l'utilisateur
        $user = $this->userRepository->create($data);

        // Assigner le rôle si spécifié
        if (isset($data['role'])) {
            $user->assignRole($data['role']);
        }

        return $user;
    }

    public function updateUser(array $data, User $user)
    {
        // Hash du mot de passe si fourni
        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            // Supprimer le mot de passe du tableau si vide
            unset($data['password']);
        }

        // Mettre à jour l'utilisateur
        $updatedUser = $this->userRepository->update($user->id, $data);

        // Mettre à jour le rôle si spécifié
        if (isset($data['role'])) {
            $updatedUser->syncRoles([$data['role']]);
        }

        return $updatedUser;
    }

    public function deleteUser(User $user)
    {
        // Vérifier que l'utilisateur n'est pas le dernier admin
        if ($user->hasRole('admin')) {
            $adminCount = User::role('admin')->count();
            if ($adminCount <= 1) {
                throw new \Exception('Impossible de supprimer le dernier administrateur.');
            }
        }

        return $this->userRepository->delete($user->id);
    }

    public function getAllRoles()
    {
        return Role::all();
    }
}
