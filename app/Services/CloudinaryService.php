<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;
use Exception;

class CloudinaryService
{
    /**
     * Upload a file to Cloudinary
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param array $options
     * @return array
     * @throws Exception
     */
    public function uploadFile(UploadedFile $file, string $folder = 'mbokatour', array $options = []): array
    {
        try {
            // Ensure Cloudinary configuration is set
            $this->ensureCloudinaryConfig();

            // Detect resource type based on file
            $resourceType = $this->detectResourceType($file);

            // Default options
            $defaultOptions = [
                'folder' => $folder,
                'resource_type' => $resourceType,
                'use_filename' => true,
                'unique_filename' => true,
                'overwrite' => false,
            ];

            // Merge with provided options
            $uploadOptions = array_merge($defaultOptions, $options);

            // Upload to Cloudinary using Upload API directly
            $uploadApi = new \Cloudinary\Api\Upload\UploadApi();
            $result = $uploadApi->upload($file->getRealPath(), $uploadOptions);

            Log::info('File uploaded to Cloudinary', [
                'public_id' => $result['public_id'],
                'secure_url' => $result['secure_url'],
                'resource_type' => $resourceType,
                'folder' => $folder
            ]);

            return [
                'public_id' => $result['public_id'],
                'secure_url' => $result['secure_url'],
                'url' => $result['url'],
                'resource_type' => $resourceType,
                'format' => $result['format'] ?? '',
                'bytes' => $result['bytes'] ?? 0,
                'width' => $result['width'] ?? 0,
                'height' => $result['height'] ?? 0,
            ];

        } catch (Exception $e) {
            Log::error('Cloudinary upload failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
                'folder' => $folder
            ]);
            throw new Exception('Failed to upload file to Cloudinary: ' . $e->getMessage());
        }
    }

    /**
     * Delete a file from Cloudinary
     *
     * @param string $publicId
     * @param string $resourceType
     * @return bool
     */
    public function deleteFile(string $publicId, string $resourceType = 'image'): bool
    {
        try {
            // Ensure Cloudinary configuration is set
            $this->ensureCloudinaryConfig();

            $uploadApi = new \Cloudinary\Api\Upload\UploadApi();
            $result = $uploadApi->destroy($publicId, [
                'resource_type' => $resourceType
            ]);

            $success = isset($result['result']) && $result['result'] === 'ok';

            if ($success) {
                Log::info('File deleted from Cloudinary', [
                    'public_id' => $publicId,
                    'resource_type' => $resourceType
                ]);
            } else {
                Log::warning('Failed to delete file from Cloudinary', [
                    'public_id' => $publicId,
                    'resource_type' => $resourceType,
                    'result' => $result
                ]);
            }

            return $success;

        } catch (Exception $e) {
            Log::error('Cloudinary delete failed', [
                'error' => $e->getMessage(),
                'public_id' => $publicId,
                'resource_type' => $resourceType
            ]);
            return false;
        }
    }

    /**
     * Detect resource type based on file
     *
     * @param UploadedFile $file
     * @return string
     */
    private function detectResourceType(UploadedFile $file): string
    {
        $mimeType = $file->getMimeType();
        
        if (str_starts_with($mimeType, 'image/')) {
            return 'image';
        } elseif (str_starts_with($mimeType, 'video/')) {
            return 'video';
        } else {
            // Default to auto for unknown types
            return 'auto';
        }
    }

    /**
     * Get optimized URL for an image with transformations
     *
     * @param string $publicId
     * @param array $transformations
     * @return string
     */
    public function getOptimizedUrl(string $publicId, array $transformations = []): string
    {
        try {
            $defaultTransformations = [
                'quality' => 'auto',
                'fetch_format' => 'auto',
            ];

            $allTransformations = array_merge($defaultTransformations, $transformations);

            // Utiliser la méthode correcte pour générer l'URL
            $uploadApi = new \Cloudinary\Api\Upload\UploadApi();
            $result = $uploadApi->explicit($publicId, [
                'type' => 'upload',
                'eager' => [$allTransformations]
            ]);

            return $result['eager'][0]['secure_url'] ?? '';

        } catch (Exception $e) {
            Log::error('Failed to generate optimized URL', [
                'error' => $e->getMessage(),
                'public_id' => $publicId
            ]);
            return '';
        }
    }

    /**
     * Extract public ID from Cloudinary URL
     *
     * @param string $url
     * @return string|null
     */
    public function extractPublicId(string $url): ?string
    {
        // Pattern to match Cloudinary URLs and extract public ID
        $pattern = '/\/(?:image|video|raw)\/upload\/(?:v\d+\/)?(.+?)(?:\.[a-zA-Z]+)?$/';
        
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }

        return null;
    }

    /**
     * Extract resource type from Cloudinary URL
     *
     * @param string $url
     * @return string
     */
    public function extractResourceType(string $url): string
    {
        if (str_contains($url, '/video/upload/')) {
            return 'video';
        } elseif (str_contains($url, '/image/upload/')) {
            return 'image';
        } elseif (str_contains($url, '/raw/upload/')) {
            return 'raw';
        }

        return 'image'; // default
    }

    /**
     * Ensure Cloudinary configuration is properly set
     *
     * @throws Exception
     */
    private function ensureCloudinaryConfig(): void
    {
        try {
            // Get configuration values
            $cloudName = config('cloudinary.cloud_name');
            $apiKey = config('cloudinary.api_key');
            $apiSecret = config('cloudinary.api_secret');

            if (empty($cloudName) || empty($apiKey) || empty($apiSecret)) {
                throw new Exception('Cloudinary configuration is missing. Please check your .env file.');
            }

            // Set Cloudinary configuration
            \Cloudinary\Configuration\Configuration::instance([
                'cloud' => [
                    'cloud_name' => $cloudName,
                    'api_key' => $apiKey,
                    'api_secret' => $apiSecret,
                ],
                'url' => [
                    'secure' => config('cloudinary.secure', true)
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Failed to configure Cloudinary', [
                'error' => $e->getMessage()
            ]);
            throw new Exception('Cloudinary configuration failed: ' . $e->getMessage());
        }
    }
}
