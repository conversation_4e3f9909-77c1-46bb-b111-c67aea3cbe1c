<?php

namespace App\Providers;

use Artesaos\SEOTools\Facades\SEOMeta;
use Artesaos\SEOTools\Facades\OpenGraph;
use Artesaos\SEOTools\Facades\TwitterCard;
use Illuminate\Support\ServiceProvider;

class SEOServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Configuration SEO de base
        SEOMeta::setTitle(config('seotools.meta.defaults.title'));
        SEOMeta::setDescription(config('seotools.meta.defaults.description'));
        SEOMeta::setKeywords(config('seotools.meta.defaults.keywords'));
        SEOMeta::setCanonical(url()->current());
        SEOMeta::setRobots('index,follow');

        // Open Graph de base
        OpenGraph::setTitle(config('seotools.opengraph.defaults.title'));
        OpenGraph::setDescription(config('seotools.opengraph.defaults.description'));
        OpenGraph::setType('website');
        OpenGraph::setUrl(url()->current());
        OpenGraph::setSiteName('MbokaTour');

        // Twitter Card de base
        TwitterCard::setType('summary_large_image');
        TwitterCard::setSite('@MbokaTour');
    }
}
