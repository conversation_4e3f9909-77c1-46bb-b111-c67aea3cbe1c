<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Spatie\Permission\Models\Role;

class ChangeUserRole extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:change-role {email} {role}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Change the role of a user';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $email = $this->argument('email');
        $roleName = $this->argument('role');

        // Vérifier que l'utilisateur existe
        $user = User::where('email', $email)->first();
        if (!$user) {
            $this->error("Utilisateur avec l'email {$email} non trouvé.");
            return 1;
        }

        // Vérifier que le rôle existe
        $role = Role::where('name', $roleName)->first();
        if (!$role) {
            $this->error("Le rôle {$roleName} n'existe pas.");
            $this->info("Rôles disponibles : " . Role::pluck('name')->implode(', '));
            return 1;
        }

        // Changer le rôle
        $user->syncRoles([$roleName]);

        $this->info("✅ Rôle de {$user->name} ({$email}) changé vers : {$roleName}");
        return 0;
    }
}
