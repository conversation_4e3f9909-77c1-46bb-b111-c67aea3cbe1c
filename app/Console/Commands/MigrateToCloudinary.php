<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Place;
use App\Models\PlaceImage;
use App\Services\CloudinaryService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\UploadedFile;
use Exception;

class MigrateToCloudinary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate:cloudinary 
                            {--dry-run : Afficher ce qui serait migré sans effectuer la migration}
                            {--force : Forcer la migration même si les fichiers existent déjà sur Cloudinary}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrer les images et vidéos existantes du stockage local vers Cloudinary';

    protected $cloudinaryService;

    public function __construct(CloudinaryService $cloudinaryService)
    {
        parent::__construct();
        $this->cloudinaryService = $cloudinaryService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('🚀 Migration vers Cloudinary - MbokaTour');
        $this->info('=====================================');

        if ($dryRun) {
            $this->warn('⚠️  Mode DRY-RUN activé - Aucune modification ne sera effectuée');
        }

        // Vérifier la configuration Cloudinary
        if (!$this->checkCloudinaryConfig()) {
            return 1;
        }

        // Migrer les images principales des places
        $this->migrateMainImages($dryRun, $force);

        // Migrer les images multiples des places
        $this->migratePlaceImages($dryRun, $force);

        $this->info('✅ Migration terminée !');
        
        if (!$dryRun) {
            $this->info('💡 Conseil : Vérifiez les logs pour voir les détails de la migration');
            $this->info('📝 Logs : storage/logs/laravel.log');
        }

        return 0;
    }

    private function checkCloudinaryConfig(): bool
    {
        $cloudName = config('cloudinary.cloud_name');
        $apiKey = config('cloudinary.api_key');
        $apiSecret = config('cloudinary.api_secret');

        if (empty($cloudName) || empty($apiKey) || empty($apiSecret)) {
            $this->error('❌ Configuration Cloudinary manquante !');
            $this->error('Configurez les variables suivantes dans votre .env :');
            $this->error('- CLOUDINARY_CLOUD_NAME');
            $this->error('- CLOUDINARY_API_KEY');
            $this->error('- CLOUDINARY_API_SECRET');
            return false;
        }

        $this->info("✅ Configuration Cloudinary trouvée (Cloud: {$cloudName})");
        return true;
    }

    private function migrateMainImages(bool $dryRun, bool $force): void
    {
        $this->info('📸 Migration des images principales...');

        $places = Place::whereNotNull('main_image_url')
                      ->where('main_image_url', '!=', '')
                      ->get();

        if ($places->isEmpty()) {
            $this->info('ℹ️  Aucune image principale à migrer');
            return;
        }

        $bar = $this->output->createProgressBar($places->count());
        $bar->start();

        $migrated = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($places as $place) {
            $bar->advance();

            $currentUrl = $place->getRawMainImagePath();

            // Vérifier si c'est déjà une URL Cloudinary
            if (str_contains($currentUrl, 'cloudinary.com')) {
                if (!$force) {
                    $skipped++;
                    continue;
                }
            }

            // Ignorer les URLs externes (Unsplash, Google, etc.)
            if (str_starts_with($currentUrl, 'http://') || str_starts_with($currentUrl, 'https://')) {
                $this->newLine();
                $this->info("ℹ️  URL externe ignorée : {$currentUrl}");
                $skipped++;
                continue;
            }

            // Construire le chemin du fichier local
            $localPath = storage_path('app/public/' . $currentUrl);

            if (!file_exists($localPath)) {
                $this->newLine();
                $this->warn("⚠️  Fichier local non trouvé : {$localPath}");
                $errors++;
                continue;
            }

            if (!$dryRun) {
                try {
                    // Créer un UploadedFile temporaire
                    $uploadedFile = new UploadedFile(
                        $localPath,
                        basename($localPath),
                        mime_content_type($localPath),
                        null,
                        true
                    );

                    // Upload vers Cloudinary
                    $result = $this->cloudinaryService->uploadFile(
                        $uploadedFile,
                        'mbokatour/places/main'
                    );

                    // Mettre à jour la base de données
                    $place->update([
                        'main_image_url' => $result['secure_url'],
                        'mediaType' => $result['resource_type']
                    ]);

                    $migrated++;
                    Log::info("Migrated main image for place {$place->id}: {$result['secure_url']}");

                } catch (Exception $e) {
                    $this->newLine();
                    $this->error("❌ Erreur pour la place {$place->id}: " . $e->getMessage());
                    $errors++;
                }
            } else {
                $migrated++;
            }
        }

        $bar->finish();
        $this->newLine();
        $this->info("📊 Images principales - Migrées: {$migrated}, Ignorées: {$skipped}, Erreurs: {$errors}");
    }

    private function migratePlaceImages(bool $dryRun, bool $force): void
    {
        $this->info('🖼️  Migration des images multiples...');

        $images = PlaceImage::whereNotNull('image_url')
                           ->where('image_url', '!=', '')
                           ->get();

        if ($images->isEmpty()) {
            $this->info('ℹ️  Aucune image multiple à migrer');
            return;
        }

        $bar = $this->output->createProgressBar($images->count());
        $bar->start();

        $migrated = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($images as $image) {
            $bar->advance();

            $currentUrl = $image->getRawImagePath();
            
            // Vérifier si c'est déjà une URL Cloudinary
            if (str_contains($currentUrl, 'cloudinary.com')) {
                if (!$force) {
                    $skipped++;
                    continue;
                }
            }

            // Construire le chemin du fichier local
            $localPath = storage_path('app/public/' . $currentUrl);
            
            if (!file_exists($localPath)) {
                $this->newLine();
                $this->warn("⚠️  Fichier non trouvé : {$localPath}");
                $errors++;
                continue;
            }

            if (!$dryRun) {
                try {
                    // Créer un UploadedFile temporaire
                    $uploadedFile = new UploadedFile(
                        $localPath,
                        basename($localPath),
                        mime_content_type($localPath),
                        null,
                        true
                    );

                    // Upload vers Cloudinary
                    $result = $this->cloudinaryService->uploadFile(
                        $uploadedFile,
                        'mbokatour/places/images'
                    );

                    // Mettre à jour la base de données
                    $image->update([
                        'image_url' => $result['secure_url']
                    ]);

                    $migrated++;
                    Log::info("Migrated place image {$image->id}: {$result['secure_url']}");

                } catch (Exception $e) {
                    $this->newLine();
                    $this->error("❌ Erreur pour l'image {$image->id}: " . $e->getMessage());
                    $errors++;
                }
            } else {
                $migrated++;
            }
        }

        $bar->finish();
        $this->newLine();
        $this->info("📊 Images multiples - Migrées: {$migrated}, Ignorées: {$skipped}, Erreurs: {$errors}");
    }
}
