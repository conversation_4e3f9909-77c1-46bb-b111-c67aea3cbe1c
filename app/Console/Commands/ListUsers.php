<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class ListUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user:list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'List all users with their roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $users = User::with('roles')->get();

        if ($users->isEmpty()) {
            $this->info('Aucun utilisateur trouvé.');
            return 0;
        }

        $headers = ['ID', 'Nom', 'Email', 'Téléphone', 'Rôles', 'Vérifié'];
        $rows = [];

        foreach ($users as $user) {
            $roles = $user->roles->pluck('name')->implode(', ') ?: 'Aucun rôle';
            $verified = $user->email_verified_at ? '✅' : '❌';
            
            $rows[] = [
                $user->id,
                $user->name,
                $user->email ?: 'Non renseigné',
                $user->phone_number,
                $roles,
                $verified
            ];
        }

        $this->table($headers, $rows);
        $this->info("Total : " . $users->count() . " utilisateur(s)");

        return 0;
    }
}
