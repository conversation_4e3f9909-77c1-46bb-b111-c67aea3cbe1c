<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class CreateAdminUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'admin:create-user {--email=} {--name=} {--phone=} {--password=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Créer un nouvel utilisateur administrateur';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Création d\'un nouvel utilisateur administrateur');

        // Récupérer les données
        $email = $this->option('email') ?: $this->ask('Email');
        $name = $this->option('name') ?: $this->ask('Nom complet');
        $phone = $this->option('phone') ?: $this->ask('Numéro de téléphone');
        $password = $this->option('password') ?: $this->secret('Mot de passe');

        // Validation
        $validator = Validator::make([
            'email' => $email,
            'name' => $name,
            'phone_number' => $phone,
            'password' => $password,
        ], [
            'email' => 'required|email|unique:users',
            'name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20',
            'password' => 'required|string|min:8',
        ]);

        if ($validator->fails()) {
            $this->error('Erreurs de validation:');
            foreach ($validator->errors()->all() as $error) {
                $this->error('- ' . $error);
            }
            return 1;
        }

        // Créer l'utilisateur
        $user = User::create([
            'name' => $name,
            'email' => $email,
            'phone_number' => $phone,
            'password' => Hash::make($password),
            'email_verified_at' => now(),
        ]);

        // Assigner le rôle admin
        $user->assignRole('admin');

        $this->info("✅ Utilisateur administrateur créé avec succès!");
        $this->info("📧 Email: {$email}");
        $this->info("👤 Nom: {$name}");
        $this->info("📱 Téléphone: {$phone}");

        return 0;
    }
}
