<?php

namespace App\Console\Commands;

use App\Models\Place;
use Illuminate\Console\Command;

class UpdatePlaceMediaTypes extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'places:update-media-types';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Auto-detect and update mediaType for all places based on their main_image_url';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to update media types for places...');

        $places = Place::whereNotNull('main_image_url')
                      ->where(function($query) {
                          $query->whereNull('mediaType')
                                ->orWhere('mediaType', '');
                      })
                      ->get();

        $count = 0;
        foreach ($places as $place) {
            $place->autoDetectMediaType();
            $count++;
            $this->line("Updated place ID {$place->id}: {$place->name} -> {$place->mediaType}");
        }

        $this->info("Successfully updated {$count} places.");

        return Command::SUCCESS;
    }
}
