<?php

namespace App\Console\Commands;

use App\Models\Place;
use App\Models\PlaceImage;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class CleanOrphanedFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'places:clean-orphaned-files {--dry-run : Show what would be deleted without actually deleting}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clean orphaned image and video files that are no longer referenced by any place';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('🔍 DRY RUN MODE - No files will be deleted');
        } else {
            $this->info('🧹 Cleaning orphaned files...');
        }

        $this->cleanMainImages($isDryRun);
        $this->cleanPlaceImages($isDryRun);

        $this->info('✅ Cleanup completed!');
    }

    /**
     * Clean orphaned main images
     */
    private function cleanMainImages(bool $isDryRun): void
    {
        $this->info('📁 Checking main images directory...');

        $files = Storage::disk('public')->files('places/main');
        $referencedFiles = Place::whereNotNull('main_image_url')
            ->pluck('main_image_url')
            ->filter(function ($path) {
                // Only include local files, not external URLs
                return $path && !filter_var($path, FILTER_VALIDATE_URL);
            })
            ->toArray();

        $orphanedFiles = array_diff($files, $referencedFiles);

        if (empty($orphanedFiles)) {
            $this->info('   ✅ No orphaned main images found');
            return;
        }

        $this->warn("   🗑️  Found " . count($orphanedFiles) . " orphaned main image(s):");

        foreach ($orphanedFiles as $file) {
            $this->line("      - {$file}");

            if (!$isDryRun) {
                Storage::disk('public')->delete($file);
                $this->info("      ✅ Deleted: {$file}");
            }
        }
    }

    /**
     * Clean orphaned place images
     */
    private function cleanPlaceImages(bool $isDryRun): void
    {
        $this->info('📁 Checking place images directory...');

        $files = Storage::disk('public')->files('places/images');
        $referencedFiles = PlaceImage::pluck('image_url')
            ->filter(function ($path) {
                // Only include local files, not external URLs
                return $path && !filter_var($path, FILTER_VALIDATE_URL);
            })
            ->toArray();

        $orphanedFiles = array_diff($files, $referencedFiles);

        if (empty($orphanedFiles)) {
            $this->info('   ✅ No orphaned place images found');
            return;
        }

        $this->warn("   🗑️  Found " . count($orphanedFiles) . " orphaned place image(s):");

        foreach ($orphanedFiles as $file) {
            $this->line("      - {$file}");

            if (!$isDryRun) {
                Storage::disk('public')->delete($file);
                $this->info("      ✅ Deleted: {$file}");
            }
        }
    }
}
