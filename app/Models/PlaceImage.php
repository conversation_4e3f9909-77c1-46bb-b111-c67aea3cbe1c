<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class PlaceImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'place_id',
        'image_url',
    ];

    public function place(): BelongsTo
    {
        return $this->belongsTo(Place::class);
    }

    /**
     * Get the full URL of the image.
     *
     * @param  string  $value
     * @return string|null
     */
    public function getImageUrlAttribute($value)
    {
        if ($value) {
            // Si c'est déjà une URL complète (comme Unsplash), la retourner telle quelle
            if (filter_var($value, FILTER_VALIDATE_URL)) {
                return $value;
            }
            // Sinon, construire l'URL locale
            return url(Storage::url($value));
        }

        return null;
    }

    /**
     * Get the raw image path (without full URL transformation)
     *
     * @return string|null
     */
    public function getRawImagePath(): ?string
    {
        return $this->attributes['image_url'] ?? null;
    }
}
