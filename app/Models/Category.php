<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Category extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'type',
        'icon',
        'color',
        'display_order',
        'description',
    ];

    public function places(): BelongsToMany
    {
        return $this->belongsToMany(Place::class);
    }

    public function events(): HasMany
    {
        return $this->hasMany(Event::class);
    }
}
