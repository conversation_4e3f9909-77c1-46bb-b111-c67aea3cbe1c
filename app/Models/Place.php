<?php

namespace App\Models;

use App\Traits\HasUuidPrimaryKey;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class Place extends Model
{
    use HasFactory, HasUuidPrimaryKey;

    /**
     * Indicates if the model's ID is auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The data type of the auto-incrementing ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    protected $fillable = [
        'name',
        'description',
        'location',
        'price',
        'is_free',
        'latitude',
        'longitude',
        'address',
        'neighborhood',
        'city',
        'opening_hours',
        'status',
        'main_image_url',
        'mediaType',
        'is_active',
        'is_featured',
        'views_count',
        'priority',
    ];

/**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Boot method to handle model events
     */
    protected static function boot()
    {
        parent::boot();

        // Événement déclenché avant la suppression d'une place
        static::deleting(function ($place) {
            Log::info("Deleting place: {$place->name} (ID: {$place->id})");

            // Supprimer l'image principale si elle existe
            $mainImagePath = $place->getRawMainImagePath();
            if ($mainImagePath && Storage::disk('public')->exists($mainImagePath)) {
                Storage::disk('public')->delete($mainImagePath);
                Log::info("Deleted main image file: {$mainImagePath}");
            }

            // Supprimer toutes les images associées
            foreach ($place->images as $image) {
                $imagePath = $image->getRawImagePath();
                if ($imagePath && Storage::disk('public')->exists($imagePath)) {
                    Storage::disk('public')->delete($imagePath);
                    Log::info("Deleted place image file: {$imagePath}");
                }
            }

            Log::info("All files deleted for place: {$place->name}");
        });
    }

 

    public function images(): HasMany
    {
        return $this->hasMany(PlaceImage::class);
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class);
    }

    public function favoritedBy(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_place_favorites');
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    public function likes(): HasMany
    {
        return $this->hasMany(PlaceLike::class);
    }

    public function likedBy(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'place_likes');
    }

    public function getMainImageUrlAttribute($value)
    {
        if ($value) {
            // Si c'est déjà une URL complète (comme Unsplash), la retourner telle quelle
            if (filter_var($value, FILTER_VALIDATE_URL)) {
                return $value;
            }
            // Sinon, construire l'URL locale
            return url(Storage::url($value));
        }

        return null;
    }

    /**
     * Get the raw main image path (without URL transformation)
     *
     * @return string|null
     */
    public function getRawMainImagePath(): ?string
    {
        return $this->attributes['main_image_url'] ?? null;
    }

    /**
     * Auto-detect and set mediaType based on main_image_url
     */
    public function autoDetectMediaType(): void
    {
        if ($this->main_image_url && empty($this->mediaType)) {
            $extension = strtolower(pathinfo($this->main_image_url, PATHINFO_EXTENSION));

            $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'bmp'];
            $videoExtensions = ['mp4', 'mov', 'avi', 'mkv', 'webm', 'ogg', 'qt'];

            if (in_array($extension, $imageExtensions)) {
                $this->mediaType = 'image';
            } elseif (in_array($extension, $videoExtensions)) {
                $this->mediaType = 'video';
            } else {
                // Default to image for unknown types
                $this->mediaType = 'image';
            }

            $this->save();
        }
    }
}
