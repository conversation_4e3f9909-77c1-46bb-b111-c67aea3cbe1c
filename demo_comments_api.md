# 💬 API Commentaires - Guide d'utilisation

## 📋 Endpoints disponibles

### 1. **<PERSON><PERSON><PERSON><PERSON>rer les commentaires d'un lieu** (Public)
```http
GET /api/places/{id}/comments
```

**Réponse :**
```json
[
  {
    "id": 1,
    "content": "Excellent restaurant avec une vue magnifique !",
    "rating": 5,
    "created_at": "2024-06-16T15:30:00.000000Z",
    "user": {
      "id": 1,
      "name": "<PERSON>"
    }
  }
]
```

### 2. **Ajouter un commentaire** (Authentifié)
```http
POST /api/places/{id}/comments
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Super endroit, je recommande vivement !",
  "rating": 5
}
```

**Réponse :**
```json
{
  "message": "Commentaire ajouté avec succès.",
  "comment": {
    "id": 2,
    "content": "Super endroit, je recommande vivement !",
    "rating": 5,
    "created_at": "2024-06-16T15:35:00.000000Z",
    "user": {
      "id": 1,
      "name": "<PERSON>"
    }
  }
}
```

### 3. **Modifier un commentaire** (Authentifié - Auteur uniquement)
```http
PUT /api/places/{place_id}/comments/{comment_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "content": "Commentaire mis à jour",
  "rating": 4
}
```

### 4. **Supprimer un commentaire** (Authentifié - Auteur uniquement)
```http
DELETE /api/places/{place_id}/comments/{comment_id}
Authorization: Bearer {token}
```

**Réponse :**
```json
{
  "message": "Commentaire supprimé avec succès."
}
```

## 🔒 Règles de sécurité

- ✅ **Lecture** : Accessible à tous (public)
- ✅ **Création** : Utilisateur authentifié uniquement
- ✅ **Modification** : Auteur du commentaire uniquement
- ✅ **Suppression** : Auteur du commentaire uniquement
- ✅ **Un commentaire par utilisateur par lieu**

## 📊 Fonctionnalités

- **Notes** : Système de notation de 1 à 5 étoiles (optionnel)
- **Modération** : Système d'approbation intégré
- **Chargement automatique** : Les commentaires sont inclus dans `GET /api/places/{id}`
- **Limitation** : Maximum 10 commentaires récents chargés automatiquement
- **Validation** : Contenu entre 3 et 1000 caractères

## 🎯 Intégration avec les lieux

Quand vous récupérez un lieu via `GET /api/places/{id}`, les commentaires sont automatiquement inclus :

```json
{
  "id": 1,
  "name": "Restaurant Le Gourmet",
  "description": "...",
  "comments": [
    {
      "id": 1,
      "content": "Excellent !",
      "rating": 5,
      "user": {
        "id": 1,
        "name": "Jean Dupont"
      }
    }
  ]
}
```
