#!/bin/bash

# Script de déploiement MbokaTour
# Usage: ./deploy.sh [environment]

set -e

ENVIRONMENT=${1:-production}
PROJECT_DIR=$(pwd)

echo "🚀 Déploiement MbokaTour - Environnement: $ENVIRONMENT"

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Vérifications préliminaires
log_info "Vérification de l'environnement..."

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "artisan" ]; then
    log_error "Ce script doit être exécuté depuis la racine du projet Laravel"
    exit 1
fi

# Vérifier Node.js
if ! command -v node &> /dev/null; then
    log_error "Node.js n'est pas installé"
    exit 1
fi

# Vérifier Composer
if ! command -v composer &> /dev/null; then
    log_error "Composer n'est pas installé"
    exit 1
fi

# Mettre l'application en mode maintenance
log_info "Activation du mode maintenance..."
php artisan down --retry=60 --secret="mbokatour-deploy-$(date +%s)" || true

# Fonction de nettoyage en cas d'erreur
cleanup() {
    log_warning "Erreur détectée, remise en ligne de l'application..."
    php artisan up
    exit 1
}

# Piège pour nettoyer en cas d'erreur
trap cleanup ERR

# Mise à jour du code (si Git)
if [ -d ".git" ]; then
    log_info "Mise à jour du code depuis Git..."
    git pull origin main
fi

# Installation des dépendances Composer
log_info "Installation des dépendances PHP..."
composer install --no-dev --optimize-autoloader --no-interaction

# Installation des dépendances Node.js
log_info "Installation des dépendances Node.js..."
npm ci --production=false

# Configuration de l'environnement
log_info "Configuration de l'environnement..."

if [ "$ENVIRONMENT" = "production" ]; then
    # Copier le fichier .env de production si il n'existe pas
    if [ ! -f ".env" ] && [ -f ".env.production.example" ]; then
        log_warning "Fichier .env manquant, copie depuis .env.production.example"
        cp .env.production.example .env
        log_warning "ATTENTION: Configurez votre fichier .env avec les bonnes valeurs!"
    fi
fi

# Générer la clé d'application si nécessaire
if ! grep -q "APP_KEY=base64:" .env; then
    log_info "Génération de la clé d'application..."
    php artisan key:generate --force
fi

# Migrations de base de données
log_info "Exécution des migrations..."
php artisan migrate --force

# Build des assets
log_info "Construction des assets frontend..."

# Définir les variables d'environnement pour le build
export NODE_OPTIONS="--max-old-space-size=1024"
export UV_THREADPOOL_SIZE=1
export VITE_MAX_WORKERS=1

# Nettoyer le cache Vite
rm -rf node_modules/.vite
rm -rf public/build

# Tentative de build avec différentes méthodes
BUILD_SUCCESS=false

# Méthode 1: Build normal
log_info "Tentative de build normal..."
if npm run build; then
    BUILD_SUCCESS=true
    log_info "Build normal réussi"
else
    log_warning "Build normal échoué, tentative avec configuration minimale..."
    
    # Méthode 2: Build minimal
    if npm run build:minimal; then
        BUILD_SUCCESS=true
        log_info "Build minimal réussi"
    else
        log_warning "Build minimal échoué, tentative avec script personnalisé..."
        
        # Méthode 3: Script personnalisé
        if ./build-production.sh; then
            BUILD_SUCCESS=true
            log_info "Build personnalisé réussi"
        fi
    fi
fi

if [ "$BUILD_SUCCESS" = false ]; then
    log_error "Tous les builds ont échoué!"
    exit 1
fi

# Optimisations Laravel
log_info "Optimisation de Laravel..."

# Cache des configurations
php artisan config:cache

# Cache des routes
php artisan route:cache

# Cache des vues
php artisan view:cache

# Cache des événements
php artisan event:cache

# Optimisation de l'autoloader
composer dump-autoload --optimize

# Nettoyage des caches
log_info "Nettoyage des caches..."
php artisan cache:clear
php artisan view:clear

# Stockage des liens symboliques
log_info "Création des liens symboliques..."
php artisan storage:link

# Permissions des fichiers
log_info "Configuration des permissions..."
chmod -R 755 storage bootstrap/cache
chmod -R 775 storage/logs

# Vérifications post-déploiement
log_info "Vérifications post-déploiement..."

# Vérifier que les assets sont présents
if [ ! -f "public/build/manifest.json" ]; then
    log_error "Le fichier manifest.json est manquant!"
    exit 1
fi

# Vérifier la connectivité à la base de données
if ! php artisan migrate:status &> /dev/null; then
    log_error "Impossible de se connecter à la base de données!"
    exit 1
fi

# Test de l'application
log_info "Test de l'application..."
if ! php artisan route:list &> /dev/null; then
    log_error "L'application ne répond pas correctement!"
    exit 1
fi

# Remise en ligne
log_info "Remise en ligne de l'application..."
php artisan up

# Nettoyage final
log_info "Nettoyage final..."
rm -rf node_modules/.vite

# Résumé du déploiement
log_info "✅ Déploiement terminé avec succès!"
echo ""
echo "📊 Résumé du déploiement:"
echo "   - Environnement: $ENVIRONMENT"
echo "   - Assets buildés: $(ls -la public/build/ | wc -l) fichiers"
echo "   - Taille du build: $(du -sh public/build/ | cut -f1)"
echo "   - Cache Laravel: Optimisé"
echo "   - Base de données: Migrée"
echo ""

# Afficher les prochaines étapes
echo "🔧 Prochaines étapes recommandées:"
echo "   1. Vérifier que l'application fonctionne: $APP_URL"
echo "   2. Tester les fonctionnalités principales"
echo "   3. Vérifier les logs: tail -f storage/logs/laravel.log"
echo "   4. Monitorer les performances"
echo ""

log_info "🎉 Déploiement MbokaTour terminé!"
